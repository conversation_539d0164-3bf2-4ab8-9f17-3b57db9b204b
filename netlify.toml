[build]
  publish = "public"
  command = "hugo --gc --minify"

[build.environment]
  HUGO_VERSION = "0.147.3"
  HUGO_ENV = "production"
  HUGO_ENABLEGITINFO = "true"
  HUGO_EXTENDED = "true"

[context.production.environment]
  HUGO_ENV = "production"

[context.deploy-preview]
  command = "hugo -b $DEPLOY_PRIME_URL"

[context.branch-deploy]
  command = "hugo -b $DEPLOY_PRIME_URL"

[context.next.environment]
  HUGO_ENABLEGITINFO = "true"

# 添加必要的內容安全策略和標頭配置，解決社交媒體爬蟲問題
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(), interest-cohort=()"
    Content-Security-Policy = "upgrade-insecure-requests"
    X-Robots-Tag = "index, follow"
    # 特別為 Facebook 和 LinkedIn 爬蟲添加的標頭
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type"

# 特別為社交媒體爬蟲添加的規則
[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
    Access-Control-Allow-Origin = "*"

# 確保正確處理 Open Graph 和 social 相關請求
[[headers]]
  for = "/*.html"
  [headers.values]
    Content-Type = "text/html; charset=UTF-8"
    Cache-Control = "public, max-age=0, must-revalidate"

# 處理可能的 502 錯誤重定向
[[redirects]]
  from = "/posts/*"
  to = "/posts/:splat"
  status = 200
  force = false

# 404 頁面處理 - 多語言支援
[[redirects]]
  from = "/zh/*"
  to = "/zh/404/"
  status = 404
  force = false

[[redirects]]
  from = "/en/*"
  to = "/en/404/"
  status = 404
  force = false

# 預設 404 處理
[[redirects]]
  from = "/*"
  to = "/404.html"
  status = 404
