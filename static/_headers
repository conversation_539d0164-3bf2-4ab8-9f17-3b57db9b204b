# Netlify _headers 文件 - 全面優化效能與安全性

# 所有路徑的標頭
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), interest-cohort=()
  X-Robots-Tag: index, follow
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, OPTIONS
  Access-Control-Allow-Headers: Content-Type
  X-XSS-Protection: 1; mode=block
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

# 靜態資源的標頭 - 長期快取策略
/images/*
  Cache-Control: public, max-age=31536000, immutable
  Access-Control-Allow-Origin: *

/css/*
  Cache-Control: public, max-age=31536000, immutable
  Access-Control-Allow-Origin: *

/js/*
  Cache-Control: public, max-age=31536000, immutable
  Access-Control-Allow-Origin: *

/fonts/*
  Cache-Control: public, max-age=31536000, immutable
  Access-Control-Allow-Origin: *

# HTML 頁面的標頭 - 動態內容快取策略
/*.html
  Content-Type: text/html; charset=UTF-8
  Cache-Control: public, max-age=3600, stale-while-revalidate=86400

# 主頁 - 更頻繁更新
/
  Cache-Control: public, max-age=1800, stale-while-revalidate=3600

# 專門針對文章頁面的標頭 - 內容快取策略
/posts/*
  Access-Control-Allow-Origin: *
  X-Robots-Tag: index, follow
  Cache-Control: public, max-age=7200, stale-while-revalidate=86400
