<svg xmlns="http://www.w3.org/2000/svg" width="800" height="500" viewBox="0 0 800 500">
  <style>
    text { font-family: 'Arial', sans-serif; }
    .title { font-size: 20px; font-weight: bold; }
    .subtitle { font-size: 16px; }
    .label { font-size: 14px; }
    .box { stroke-width: 2px; rx: 6px; ry: 6px; }
    .arrow { stroke-width: 2px; marker-end: url(#arrowhead); }
    .comparebox { stroke: #333; fill: #f8f9fa; }
  </style>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 標題 -->
  <text x="400" y="40" class="title" text-anchor="middle">水平擴展方法對比</text>
  
  <!-- 分片策略 -->
  <rect x="50" y="80" width="200" height="120" class="box" fill="#e6f7ff" stroke="#1890ff" />
  <text x="150" y="110" class="subtitle" text-anchor="middle" fill="#1890ff">基於分片的水平擴展</text>
  <text x="150" y="140" class="label" text-anchor="middle">• 按鍵值分片</text>
  <text x="150" y="165" class="label" text-anchor="middle">• 按範圍分片</text>
  <text x="150" y="190" class="label" text-anchor="middle">• 一致性哈希</text>
  
  <!-- 副本策略 -->
  <rect x="300" y="80" width="200" height="120" class="box" fill="#f6ffed" stroke="#52c41a" />
  <text x="400" y="110" class="subtitle" text-anchor="middle" fill="#52c41a">基於副本的水平擴展</text>
  <text x="400" y="140" class="label" text-anchor="middle">• 主從複製</text>
  <text x="400" y="165" class="label" text-anchor="middle">• 多主複製</text>
  <text x="400" y="190" class="label" text-anchor="middle">• 異地複製</text>
  
  <!-- 分區策略 -->
  <rect x="550" y="80" width="200" height="120" class="box" fill="#fff7e6" stroke="#fa8c16" />
  <text x="650" y="110" class="subtitle" text-anchor="middle" fill="#fa8c16">基於分區的水平擴展</text>
  <text x="650" y="140" class="label" text-anchor="middle">• 功能分區</text>
  <text x="650" y="165" class="label" text-anchor="middle">• 資料分區</text>
  <text x="650" y="190" class="label" text-anchor="middle">• 混合分區</text>
  
  <!-- 比較表格 -->
  <rect x="100" y="240" width="600" height="220" class="box comparebox" />
  
  <!-- 表頭 -->
  <line x1="100" y1="280" x2="700" y2="280" stroke="#333" stroke-width="1" />
  <text x="150" y="265" class="label" text-anchor="middle" font-weight="bold">方法</text>
  <text x="300" y="265" class="label" text-anchor="middle" font-weight="bold">優點</text>
  <text x="500" y="265" class="label" text-anchor="middle" font-weight="bold">缺點</text>
  <text x="650" y="265" class="label" text-anchor="middle" font-weight="bold">適用場景</text>
  
  <!-- 分片行 -->
  <line x1="100" y1="330" x2="700" y2="330" stroke="#333" stroke-width="1" stroke-dasharray="3,3" />
  <text x="150" y="310" class="label" text-anchor="middle" fill="#1890ff">分片</text>
  <text x="300" y="305" class="label" font-size="12">• 高度可擴展性</text>
  <text x="300" y="325" class="label" font-size="12">• 查詢並行處理</text>
  <text x="500" y="305" class="label" font-size="12">• 分片邏輯複雜</text>
  <text x="500" y="325" class="label" font-size="12">• 跨分片事務難</text>
  <text x="650" y="310" class="label" font-size="12">高吞吐量系統</text>
  
  <!-- 副本行 -->
  <line x1="100" y1="380" x2="700" y2="380" stroke="#333" stroke-width="1" stroke-dasharray="3,3" />
  <text x="150" y="360" class="label" text-anchor="middle" fill="#52c41a">副本</text>
  <text x="300" y="355" class="label" font-size="12">• 高可用性</text>
  <text x="300" y="375" class="label" font-size="12">• 讀取性能提升</text>
  <text x="500" y="355" class="label" font-size="12">• 一致性挑戰</text>
  <text x="500" y="375" class="label" font-size="12">• 寫入性能不變</text>
  <text x="650" y="360" class="label" font-size="12">讀多寫少系統</text>
  
  <!-- 分區行 -->
  <text x="150" y="410" class="label" text-anchor="middle" fill="#fa8c16">分區</text>
  <text x="300" y="405" class="label" font-size="12">• 模組化</text>
  <text x="300" y="425" class="label" font-size="12">• 隔離性好</text>
  <text x="500" y="405" class="label" font-size="12">• 跨分區查詢困難</text>
  <text x="500" y="425" class="label" font-size="12">• 設計難度大</text>
  <text x="650" y="410" class="label" font-size="12">微服務架構</text>
</svg>
