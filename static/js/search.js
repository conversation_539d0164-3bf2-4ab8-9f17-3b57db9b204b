// Search functionality JavaScript

var searchQuery = param("s");
if (searchQuery) {
  document.getElementById("search-query").setAttribute("value", searchQuery);
  executeSearch(searchQuery);
} else {
  document.getElementById("search-results").innerHTML = '<div class="search-results-empty">Please enter search keywords</div>';
}

function executeSearch(searchQuery) {
  fetch(indexURL)
    .then(function(response) {
      return response.json();
    })
    .then(function(data) {
      var results = [];
      data.forEach(function(item) {
        if (
          item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (item.tags && item.tags.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (item.categories && item.categories.toLowerCase().includes(searchQuery.toLowerCase()))
        ) {
          var snippet = "";
          var content = item.content.toLowerCase();
          var searchPosition = content.indexOf(searchQuery.toLowerCase());
          
          if (searchPosition > -1) {
            var start = Math.max(0, searchPosition - 50);
            var end = Math.min(content.length, searchPosition + searchQuery.length + 50);
            snippet = content.substring(start, end);
            
            // Add ellipsis
            if (start > 0) {
              snippet = "..." + snippet;
            }
            if (end < content.length) {
              snippet = snippet + "...";
            }
          } else {
            // If no direct match, display the first 100 characters
            snippet = content.substring(0, 100) + "...";
          }
          
          results.push({
            title: item.title,
            link: item.permalink,
            snippet: snippet,
            tags: item.tags,
            categories: item.categories
          });
        }
      });
      
      displayResults(results);
    });
}

function displayResults(results) {
  var searchResults = document.getElementById("search-results");
  
  if (results.length > 0) {
    var template = document.getElementById("search-result-template").innerHTML;
    var html = "";
    
    results.forEach(function(result, i) {
      var output = template
        .replace(/\${title}/g, result.title)
        .replace(/\${link}/g, result.link)
        .replace(/\${snippet}/g, result.snippet)
        .replace(/\${key}/g, i);
      
      if (result.tags) {
        output = output
          .replace(/\${ isset tags }/g, "")
          .replace(/\${ end }/g, "")
          .replace(/\${tags}/g, result.tags);
      } else {
        output = output
          .replace(/\${ isset tags }[\s\S]*?\${ end }/g, "");
      }
      
      if (result.categories) {
        output = output
          .replace(/\${ isset categories }/g, "")
          .replace(/\${ end }/g, "")
          .replace(/\${categories}/g, result.categories);
      } else {
        output = output
          .replace(/\${ isset categories }[\s\S]*?\${ end }/g, "");
      }
      
      html += output;
    });
    
    searchResults.innerHTML = html;
  } else {
    searchResults.innerHTML = '<div class="search-results-empty">No matching results found</div>';
  }
}

function param(name) {
  return decodeURIComponent((location.search.split(name + '=')[1] || '').split('&')[0]).replace(/\+/g, ' ');
}
