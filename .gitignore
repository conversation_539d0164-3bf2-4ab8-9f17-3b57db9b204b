# <PERSON> 靜態網站輸出目錄 (Hugo generated public folder)
public/

# Hugo 產生的資源快取目錄 (Hugo generated resources)
/resources/_gen/
/assets/jsconfig.json
hugo_stats.json

# Hugo 建置暫存檔案 (Hugo build temporary files)
/.hugo_build.lock

# Hugo 可執行檔 (Hugo executables)
hugo.exe
hugo.darwin
hugo.linux

# Node 相關檔案 (Node related files)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 作業系統產生的暫存檔 (OS generated files)
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 編輯器暫存檔 (Editor temporary files)
*.swp
*.swo
*~

# IDE 與編輯器設定 (IDE and editor settings)
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw*

# 建置與發布相關 (Build and deployment related)
dist/
vendor/
.netlify/
