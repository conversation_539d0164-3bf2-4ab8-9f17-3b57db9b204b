# Hugo 架站超簡單入門：從零到線上 For Dummies

![Hugo Logo](https://gohugo.io/img/hugo-logo-wide.svg)

歡迎來到網站建置的奇妙世界！如果你手上拿著這份指南，八成是聽說了「架個網站好像很酷」，但一想到程式碼、伺服器、資料庫...頭就開始痛了。別怕！這份指南就是你的救星，專為像你一樣，對架站一竅不通，但又充滿好奇與勇氣的「Dummies」所設計。

我們要介紹的主角叫做 **Hugo**。它就像是網站界的超級英雄，速度飛快、金剛不壞。讀完這份教學，你將能從一個連「終端機」都沒開過的麻瓜，搖身一變，成為擁有自己專屬網站的魔法師。

準備好了嗎？泡杯咖啡，找張舒服的椅子，讓我們一起踏上這趟神奇的架站之旅吧！

---

## 第 1 章：為什麼選 Hugo？與 WordPress 的終極大對決

在我們動手之前，你可能會問：「嘿，我只聽過 WordPress，為什麼我們要用這個叫 Hugo 的怪東西？」

問得好！這就像買車前，你總得知道法拉利跟家庭休旅車的差別吧？

### 1.1 什麼是靜態網站？什麼又是動態網站？

想像一下，你走進一家圖書館。

*   **靜態網站 (Static Site)**：就像一本已經印好的書。書裡的每個字、每張圖都固定在那裡。任何人來翻，看到的內容都一模一樣。它很單純，翻頁速度超快，而且除非有人把書燒了，不然內容不會變。**Hugo 就是專門製作這種「書」的工廠。**

*   **動態網站 (Dynamic Site)**：就像一位會跟你聊天的圖書館員。你問他：「最近有什麼熱門的科幻小說？」他會立刻從腦中（資料庫）找出資料，整理好（伺服器處理），然後告訴你。每次不同的人問，或不同時間問，他給的答案可能都不同。**WordPress 就是這種超能圖書館員。**

### 1.2 Hugo vs. WordPress：世紀大對決

讓我們用一張表格，看看這兩位選手的實力如何：

| 特點 | Hugo (靜態網站產生器) | WordPress (動態內容管理系統) |
| :--- | :--- | :--- |
| **核心概念** | 你先寫好內容 (Markdown)，Hugo 幫你「烤」成一整個網站 (HTML/CSS/JS)，然後你把烤好的成品放上網。 | 你在一個管理後台寫文章，當有人瀏覽你的網站時，WordPress 才即時從資料庫撈資料、組合頁面給他看。 |
| **速度** | 🚀 **快如閃電**。因為網站是預先生成好的 HTML 檔案，沒有資料庫查詢、沒有伺服器處理，載入速度極快。 | 🐢 **相對較慢**。每次瀏覽都需要伺服器和資料庫的即時運算，如果外掛太多或主機不夠力，就會像烏龜賽跑。 |
| **安全性** | 🛡️ **固若金湯**。沒有資料庫、沒有複雜的後台登入頁面，駭客想攻擊也找不到門路。就像一座沒有門窗的堡壘。 | 🔓 **漏洞較多**。WordPress 是全球最流行的系統，也是駭客最愛的攻擊目標。你需要時常更新核心、主題、外掛，否則很容易被黑。 |
| **維護成本** | 🧘 **極度省心**。網站上線後幾乎不用管它。沒有需要天天更新的外掛，也不用擔心資料庫掛掉。 | 🤯 **需要操心**。你需要定期登入後台，更新所有東西，處理外掛衝突，還要應付垃圾留言。像個需要隨時照顧的寶寶。 |
| **學習曲線** |  steeper **前期稍陡**。你需要學一點點指令和 Markdown 語法，但一旦上手，海闊天空。 | easier **前期平緩**。有圖形化介面，點點滑鼠就能開始。但要深入客製化或解決問題時，難度會飆升。 |
| **硬體需求** | 💻 **極低**。你甚至可以把網站放在最便宜、最陽春的虛擬主機上，它照樣跑得飛快。 | 💰 **較高**。需要支援 PHP 和 MySQL 資料庫的主機，效能不能太差，否則網站會很卡。 |
| **彈性與自由度** | 🎨 **極高**。你可以完全掌控網站的每一個角落，不受主題或外掛的限制。 | 🔗 **受限於主題與外掛**。雖然有數萬個選擇，但你終究是在一個框架內玩。想做個大改動？可能得花錢請工程師。 |

**一句話總結**：

*   **選擇 Hugo**：如果你想要一個**超快、超安全、免維護**的個人部落格、作品集網站或中小型企業官網，而且不介意前期花一點點時間學習。
*   **選擇 WordPress**：如果你需要一個功能極其複雜的網站（例如：購物車、會員系統、論壇），且希望有大量的現成外掛可用，並且不介意花錢買好主機和花時間維護。

對於新手來說，Hugo 就像給你一套頂級的樂高積木，雖然一開始要看說明書，但蓋出來的成品堅固又漂亮。WordPress 則像給你一個全自動的玩具工廠，按鈕很多，但機器何時會故障，你永遠不知道。

---

## 第 2 章：工具準備：你的第一個網站開發環境

在蓋房子前，總得先準備好槌子、釘子和捲尺吧？架設網站也一樣。別擔心，這些工具全都免費，而且安裝起來比你想像的簡單。

### 2.1 你的數位瑞士刀：Visual Studio Code (VS Code)

忘掉 Windows 內建的「記事本」或 Mac 的「文字編輯」。我們要用的是專業開發者也在用的神器：**VS Code**。

*   **它是什麼？** 一個免費、強大且超級帥的程式碼編輯器。
*   **為什麼要用它？**
    *   **語法高亮**：它會幫你的程式碼塗上不同顏色，讓你一目了然，不會看走眼。
    *   **內建終端機**：你可以在同一個視窗裡，一邊寫程式，一邊下指令，不用切換視窗切到手忙腳亂。
    *   **擴充功能**：它有個像 App Store 一樣的市集，有無數的擴充功能可以安裝，讓你的編輯器變得更強大。

**如何安裝？**

1.  打開瀏覽器，前往 [VS Code 官方網站](https://code.visualstudio.com/)。
2.  網站會自動偵測你的作業系統（Windows / macOS / Linux），點擊那個大大的藍色下載按鈕。
3.  下載完成後，像安裝普通軟體一樣，點兩下安裝檔，然後一直按「下一步」就對了。

![VS Code Website](https://i.imgur.com/jZ3aXwM.png)

### 2.2 與電腦對話的密道：終端機 (Terminal)

終端機是你跟電腦直接溝通的工具。你看電影裡駭客快速敲打的那個黑底綠字視窗？就是它！但別怕，它不是駭客專屬，而是我們架站的好夥伴。

*   **macOS**: 叫做「**終端機 (Terminal)**」。你可以在 `應用程式 > 工具程式` 裡找到它，或者直接按 `Cmd + 空白鍵` 搜尋「Terminal」。
*   **Windows**: 傳統上叫做「**命令提示字元 (cmd)**」，但我們推薦使用更現代、更強大的「**PowerShell**」或直接使用 VS Code 內建的終端機。當你安裝了 Git (下面會提到)，你還會得到一個超好用的「**Git Bash**」。
*   **Linux**: 你肯定知道它在哪裡。它通常就叫做「**Terminal**」。

**基本指令暖身：**

打開你的終端機，試著輸入看看：

*   `pwd` (Print Working Directory): 告訴你「我們現在在哪個資料夾」。
*   `ls` (List): 列出目前資料夾裡的所有檔案和資料夾 (Windows PowerShell/cmd 中對應的指令是 `dir`)。
*   `cd <資料夾名稱>` (Change Directory): 進入指定的資料_夾。例如 `cd Desktop` 就是前往桌面。

### 2.3 程式碼的時光機：Git 與 GitHub

這兩個東西是現代開發者的必備神器，但新手常常搞混。

*   **Git**: 想像它是一台**安裝在你電腦上的時光機**。每當你對你的網站做了修改（比如寫了新文章），你就可以用 Git 拍一張「快照」。如果你不小心把網站改壞了，隨時可以坐時光機回到任何一個快照的時間點。這叫做「**版本控制**」。

*   **GitHub**: 想像它是一個**在雲端的超大保險庫**，專門存放你的 Git 時光機快照。你可以把你在電腦上拍的快照全部同步到 GitHub 上。這樣一來：
    1.  **備份**：就算你的電腦爆炸了，你的網站程式碼也安全地在雲端。
    2.  **協作**：你可以邀請朋友一起來蓋網站，他們可以從 GitHub 下載你的進度，修改後再上傳。
    3.  **部署**：很多雲端平台（我們後面會講）可以直接從你的 GitHub 保險庫拿程式碼，自動幫你把網站上線！

**如何安裝 Git？**

1.  前往 [Git 官方網站](https://git-scm.com/downloads)。
2.  網站會自動偵測你的作業系統，點擊下載。
3.  安裝過程基本上也是一直按「下一步」。Windows 使用者請注意，在選擇預設編輯器時，可以選 VS Code。在選擇 PATH 環境時，請選擇「Git from the command line and also from 3rd-party software」。

**如何註冊 GitHub？**

1.  前往 [GitHub 官方網站](https://github.com/)。
2.  跟註冊任何網站服務一樣，設定你的使用者名稱、Email 和密碼。
3.  完成！你現在擁有全世界最大的程式碼保險庫的帳號了。

---

## 第 3 章：Hugo 安裝：一步步帶你搞定

主角終於要登場了！安裝 Hugo 的過程比你想像的簡單，但不同作業系統有不同的「咒語」。我們會一步步帶你完成。

**重要提示：** 我們強烈建議安裝 **Hugo Extended Version (擴充版)**。很多漂亮的主題都會用到一些高級功能，只有擴充版才支援。我們的教學指令都會以安裝擴充版為主。

### 3.1 安裝流程圖 (Mermaid)

讓我們用一張圖來概覽整個安裝過程：

```mermaid
graph TD
    A[開始] --> B{選擇你的作業系統};
    B --> C[macOS];
    B --> D[Windows];
    B --> E[Linux];

    C --> C1[安裝 Homebrew];
    C1 --> C2[執行 brew install hugo];
    C2 --> F{驗證安裝};

    D --> D1[安裝 Chocolatey];
    D1 --> D2[執行 choco install hugo-extended];
    D2 --> F;

    E --> E1[下載 .deb/.rpm 檔案];
    E1 --> E2[執行安裝指令];
    E2 --> F;

    F --> G[輸入 hugo version];
    G --> H{看到版本號？};
    H -- 是 --> I[🎉 恭喜！安裝成功！];
    H -- 否 --> J[😱 糟糕！檢查錯誤訊息];
```

### 3.2 macOS 使用者的快速通道：Homebrew

如果你是 Mac 用戶，Homebrew 是你的好朋友。它是一個套件管理器，可以讓你用一行指令就安裝好各種開發工具。

1.  **檢查是否已安裝 Homebrew**
    打開「終端機」，輸入以下指令後按 Enter：
    ```bash
    brew --version
    ```
    如果看到版本號，代表你已經裝好了，請跳到第 3 步。如果顯示 `command not found`，請繼續下一步。

2.  **安裝 Homebrew**
    在終端機裡，複製並貼上以下這行來自 [Homebrew 官網](https://brew.sh) 的指令，然後按 Enter。它會自動完成所有安裝工作。
    ```bash
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    ```
    安裝過程會請你輸入電腦密碼，輸入時不會顯示任何字元，這是正常的，勇敢地輸完按 Enter 就好。

3.  **安裝 Hugo**
    現在，用 Homebrew 來安裝 Hugo，就像去便利商店買飲料一樣簡單。在終端機輸入：
    ```bash
    brew install hugo
    ```
    Homebrew 會自動幫你安裝最新版的 Hugo **Extended** 版本。

4.  **驗證安裝**
    輸入以下指令：
    ```bash
    hugo version
    ```
    如果你看到類似 `hugo v0.128.2-d6d49163ec25 ... darwin/amd64 Extended` 的輸出，恭喜你，成功了！注意看有沒有「**Extended**」這個字喔！

### 3.3 Windows 使用者的魔法棒：Chocolatey

Windows 使用者，你們也有自己的套件管理器！我們推薦 Chocolatey。

1.  **安裝 Chocolatey**
    首先，你需要用「**系統管理員權限**」打開 **PowerShell**。
    *   在開始功能表按右鍵，選擇「Windows PowerShell (系統管理員)」。
    *   然後，到 [Chocolatey 官網](https://chocolatey.org/install)，複製那段安裝指令（通常是 `Set-ExecutionPolicy Bypass ...` 開頭的那一大串），貼到 PowerShell 裡執行。

2.  **安裝 Hugo**
    安裝好 Chocolatey 後，關掉目前的 PowerShell 視窗，再**重新用系統管理員權限開一個新的**。然後輸入：
    ```powershell
    choco install hugo-extended -y
    ```
    `-y` 的意思是自動回答「yes」，幫你省去確認的步驟。

3.  **驗證安裝**
    打開一個**新的** PowerShell 視窗（這次不用管理員權限了），輸入：
    ```powershell
    hugo version
    ```
    同樣地，看到版本號和「**Extended**」字樣，就代表大功告成！

### 3.4 Linux 使用者的榮耀之路

Linux 高手們，你們的選擇就多了。你可以從 Snap、Homebrew on Linux 或直接下載二進位檔。我們這裡推薦最通用的方法：直接從 GitHub 下載。

1.  **前往 Hugo 發布頁面**
    打開瀏覽器，前往 [Hugo GitHub Releases](https://github.com/gohugoio/hugo/releases)。

2.  **下載對應的檔案**
    你會看到一長串檔案列表。找到符合你系統架構的 `hugo_extended_..._Linux-64bit.deb` (Debian/Ubuntu) 或 `hugo_extended_..._Linux-64bit.rpm` (Fedora/CentOS) 檔案，點擊下載。

3.  **安裝檔案**
    打開你的終端機，`cd` 到你下載檔案的資料夾（通常是 `Downloads`）。
    *   **對於 .deb 檔案 (Ubuntu/Debian):**
        ```bash
        sudo dpkg -i hugo_extended_*.deb
        ```
    *   **對於 .rpm 檔案 (Fedora/CentOS):**
        ```bash
        sudo rpm -i hugo_extended_*.rpm
        ```

4.  **驗證安裝**
    最後的儀式！
    ```bash
    hugo version
    ```
    看到勝利的曙光了嗎？

### 3.5 疑難雜症排除 (Troubleshooting)

*   **`hugo: command not found`**:
    *   **原因**：最常見的原因是你的「環境變數 PATH」沒有設定好。意思是，終端機不知道要去哪裡找 `hugo` 這個指令。
    *   **解決**：
        1.  重開你的終端機或 VS Code。有時候只是需要一個新的 session 來讀取設定。
        2.  如果你是手動安裝（例如下載 zip 檔解壓縮），你需要手動把 Hugo 執行檔所在的資料夾路徑，加入到系統的 PATH 環境變數裡。這個步驟比較進階，建議 Google 搜尋「如何設定 Windows/Mac 環境變數 PATH」。但如果你是照著我們的教學用套件管理器安裝的，基本上不會遇到這個問題。

*   **安裝的不是 Extended 版本**:
    *   **原因**：可能是不小心用了 `choco install hugo` 或下載了沒有 `extended` 字樣的檔案。
    *   **解決**：先解除安裝舊的 (`brew uninstall hugo` 或 `choco uninstall hugo`)，然後用我們上面提供的正確指令重新安裝一次。

---

## 第 4 章：建立網站：讓你的網站活起來

萬事俱備，只欠東風！現在，我們要來真正建立並「啟動」你的網站了。

### 4.1 你的專案結構深度導覽

通常，學習 Hugo 的第一步是執行 `hugo new site <網站名稱>`。但別急！你現在所在的專案 (`parsa-site`) 已經是一個功能齊全的範例網站了。讓我們直接從分析它的結構開始，這比從零開始一個空資料夾更有趣！

打開 VS Code，並將 `parsa-site` 這個資料夾拖曳進去。你會在左邊的檔案總管看到類似這樣的結構：

```
.
├── archetypes/
├── assets/
├── content/
├── data/
├── layouts/
├── static/
├── themes/
└── hugo.toml
```

這就是 Hugo 網站的骨架。讓我們來看看每個資料夾是做什麼的：

| 資料夾/檔案 | 功能說明 | Dummies 比喻 |
| :--- | :--- | :--- |
| `hugo.toml` | **網站的大腦**。這是最重要的設定檔，定義了網站標題、語言、網址、選單、以及所有主題相關的設定。 | 就像一本「網站使用說明書」，所有基本資料都寫在這裡。 |
| `content/` | **網站的靈魂**。你所有的文章、頁面內容（Markdown 檔案）都放在這裡。資料夾結構會直接對應到你的網站網址。 | 你的書櫃，每一本書就是一篇文章。`content/blog/hello.md` 就會變成 `your-site.com/blog/hello/`。 |
| `static/` | **網站的衣櫃**。存放所有靜態檔案，如圖片、CSS、JavaScript、PDF 等。這裡的檔案會被原封不動地複製到最終的網站裡。 | 你的衣櫃和道具間。`static/images/me.jpg` 會出現在 `your-site.com/images/me.jpg`。 |
| `layouts/` | **網站的設計藍圖**。存放 HTML 樣板檔案。Hugo 會用這裡的樣板，去「渲染」`content` 裡的內容。這是最能發揮客製化的地方。 | 你的排版設計工作室。你可以決定文章頁面長怎樣、首頁長怎樣。 |
| `themes/` | **主題樣板間**。存放你下載的佈景主題。你現在用的就是 `parsa` 主題。**新手鐵則：絕對不要直接修改這個資料夾裡的檔案！** | 買來的現成裝潢風格包。你可以參考，但不要直接在上面塗鴉。要改裝潢，請到 `layouts/`。 |
| `assets/` | **高級素材庫**。給 Hugo 處理的進階資源，例如 SCSS (一種更強的 CSS) 或需要壓縮的 JavaScript。Hugo 會在建置時處理它們。 | 專業的材料加工廠。把原料放進去，Hugo 會幫你處理成最終產品。 |
| `archetypes/` | **文章模板**。當你用 `hugo new content/blog/new-post.md` 新增文章時，Hugo 會用這裡的模板來建立檔案的預設內容（比如標題、日期）。 | 你的「文章產生器」範本。可以預設好每篇文章該有哪些基本設定。 |

### 4.2 點燃引擎：啟動本地伺服器

這是最神奇的時刻！我們要讓這個網站真正在你的電腦上「活起來」。

1.  **打開 VS Code 的內建終端機**
    在 VS Code 的頂端選單，選擇 `Terminal > New Terminal` (或 `檢視 > 終端機`)。下方會彈出一個終端機視窗，而且路徑已經自動設定在你的專案資料夾了。超方便！

2.  **輸入魔法指令**
    在終端機裡，輸入以下指令，然後按下 Enter：
    ```bash
    hugo server -D
    ```

    讓我們分解一下這個咒語：
    *   `hugo`: 呼叫 Hugo 大神。
    *   `server`: 告訴大神，我們要啟動內建的「即時預覽伺服器」。
    *   `-D`: 是 `--buildDrafts` 的縮寫，意思是「連同草稿一起顯示」。文章的 front matter (我們等下會講) 中如果設定了 `draft: true`，預設是不會顯示的，加上 `-D` 才能在本地預覽時看到它們。

3.  **見證奇蹟**
    如果一切順利，你會看到類似這樣的輸出：
    ```
    Web Server is available at http://localhost:1313/ (bind address 127.0.0.1)
    Press Ctrl+C to stop
    ```

    這代表 Hugo 已經在你的電腦上，悄悄地建立了一個微型網站伺服器。

4.  **打開你的網站！**
    按住 `Ctrl` (Windows) 或 `Cmd` (macOS)，然後用滑鼠點擊終端機裡顯示的 `http://localhost:1313/` 連結。

    **Booooom!** 你的瀏覽器會打開一個新分頁，顯示出一個完整、漂亮的網站！這就是你的網站的「本地預覽版」。

**最酷的部分來了：**

試著去 `content/zh/blog/web-design-principles.md` 這個檔案，隨便修改一些文字，然後按下 `Ctrl+S` 儲存。你會發現，幾乎在你儲存的同一時間，瀏覽器裡的網頁**自動重新整理**了！

這就是 Hugo `server` 的威力：它會即時監控你的所有檔案變更，並立刻重新建置網站，讓你能夠即時預覽修改結果。這讓開發和寫作體驗變得超級流暢！

### 4.3 停止伺服器

當你今天不想玩了，要怎麼關掉這個本地伺服器呢？

回到 VS Code 的終端機視窗，按下 `Ctrl + C`。它會問你是否要終止，再按一次 `Ctrl + C` 或輸入 `y` 即可。伺服器就關閉了，`http://localhost:1313/` 也會連不上。

---

## 第 5 章：美化網站：主題、圖片與 Markdown 教學

你的網站已經跑起來了，但現在它看起來是「別人的」網站。接下來，我們要學習如何把它變成你自己的樣子。

### 5.1 內容為王：Markdown 語法速成

Markdown 是一種輕量級的標記語言，讓你用簡單的純文字符號，就能寫出有格式的漂亮文件。你現在看到的這份教學，就是用 Markdown 寫的！

打開 `content/zh/blog/business-innovation.md` 這個檔案來看看。你會看到檔案最上方有一塊用 `---` 包起來的區域，這叫做 **Front Matter**，是這篇文章的「設定區」。

```yaml
---
title: "商業模式創新：如何在數位時代中脫穎而出"
date: 2024-07-20T10:00:00+08:00
image: "images/post/business-innovation.png"
author: "陳大明"
categories: ["商業策略"]
tags: ["創新", "數位轉型"]
draft: false
---
```

*   **title**: 文章標題。
*   **date**: 發布日期。
*   **image**: 這篇文章的特色圖片，路徑是相對於 `static` 資料夾。
*   **author**: 作者。
*   **categories/tags**: 分類和標籤，有利於管理和搜尋。
*   **draft**: 是否為草稿。`false` 代表是正式文章。

`---` 下方的部分，就是文章的正文，使用 Markdown 語法撰寫。

**常用 Markdown 語法一覽表：**

| 功能 | Markdown 語法 | 範例 |
| :--- | :--- | :--- |
| **標題** | `# H1`, `## H2`, `### H3` | `## 這是一個二級標題` |
| **粗體** | `**文字**` 或 `__文字__` | `這是 **重要** 的文字` |
| **斜體** | `*文字*` 或 `_文字_` | `這是 _強調_ 的文字` |
| **清單 (無序)** | `- 項目一`<br>`- 項目二` | `- 蘋果<br>- 香蕉` |
| **清單 (有序)** | `1. 項目一`<br>`2. 項目二` | `1. 起床<br>2. 刷牙` |
| **連結** | `[連結文字](網址)` | `[點我到 Google](https://google.com)` |
| **圖片** | `![圖片替代文字](圖片路徑)` | `![我的貓]( /images/my-cat.jpg )` |
| **引言** | `> 引用的文字` | `> To be or not to be.` |
| **程式碼 (行內)** | `` `code` `` | `執行 `hugo server` 指令` |
| **程式碼 (區塊)** | ` ``` `<br>`程式碼`<br>` ``` ` | ` ```html `<br>`<h1>Hello</h1>`<br>` ``` ` |

**動手試試看：**

1.  用 `hugo server -D` 啟動伺服器。
2.  在 VS Code 中，建立一個新檔案：`content/zh/blog/my-first-post.md`。
3.  把 `business-innovation.md` 的內容複製貼上到你的新檔案中。
4.  修改 Front Matter 的 `title`, `date` 等等。
5.  在正文部分，用上面學到的 Markdown 語法，寫下你的第一篇文章！
6.  儲存檔案，然後去瀏覽器看看你的大作！它應該會自動出現在部落格列表裡。

### 5.2 讓網站賞心悅目：圖片處理

一張好的圖片勝過千言萬語。在 Hugo 中加入圖片非常簡單。

1.  **把你的圖片檔案** (例如 `my-awesome-photo.jpg`) 放到 `static/images/` 資料夾底下。你可以再建立子資料夾來管理，例如 `static/images/my-posts/`。
2.  **在 Markdown 中引用它**。路徑要從 `static` 的根目錄開始算，所以要寫成 `/images/my-awesome-photo.jpg`。
    ```markdown
    ![這是我超讚的照片](/images/my-awesome-photo.jpg)
    ```

**Pro Tip:** 圖片檔案太大會拖慢網站速度。在上傳前，可以使用像 [TinyPNG](https://tinypng.com/) 這樣的線上工具來壓縮你的圖片，可以在不犧牲太多畫質的情況下，大幅減少檔案大小。

### 5.3 更換與客製化主題

你目前的網站使用的是 `parsa` 主題。如果你想換個風格，可以到 [Hugo 官方主題網站](https://themes.gohugo.io/) 逛逛。

**更換主題的步驟（高階，先了解即可）：**

1.  選擇一個你喜歡的主題。
2.  按照主題的說明文件，將它加入到你的 `themes` 資料夾，通常是透過 `git submodule add` 的方式。
3.  修改 `hugo.toml` 檔案，將 `theme = "parsa"` 改成 `theme = "新主題名稱"`。
4.  **最重要也最麻煩的一步**：每個主題的設定 (`hugo.toml` 裡的參數) 都不太一樣。你需要仔細閱讀新主題的說明文件，修改你的 `hugo.toml` 來符合它的要求。

**客製化目前的主題 (新手建議這樣做)：**

**黃金鐵則：絕對不要、絕對不要、絕對不要直接修改 `themes/parsa/` 裡面的任何檔案！**

為什麼？因為當主題作者發布更新時，你一更新，所有修改就全被覆蓋了，你會欲哭無淚。

Hugo 有一個非常聰明的**覆蓋 (override) 機制**。它的尋找檔案順序是：

1.  先找專案根目錄的 `layouts/`
2.  再找 `themes/你的主題/layouts/`

這表示，如果 `themes/parsa/layouts/partials/footer.html` 是主題的頁尾檔案，你**不應該**去改它。你應該：

1.  在你的專案根目錄，建立一個 `layouts/partials/` 資料夾。
2.  把**原始的** `themes/parsa/layouts/partials/footer.html` **複製**一份到你剛建立的 `layouts/partials/footer.html`。
3.  現在，你可以隨心所欲地修改這個**複製出來的檔案**了！Hugo 會優先使用你這個版本，而忽略主題原本的版本。

這樣一來，就算主題更新了，你的客製化修改也安然無恙。這個原則適用於 `layouts`, `static`, `assets` 等所有可覆蓋的資料夾。

### 5.4 自訂首頁「商業解決方案」區塊的內容

現在，你的首頁上「商業解決方案 (Business Solutions)」這個區塊，不再是自動抓取最新的文章了！你可以精準地控制要顯示哪些文章，讓這個區塊真正符合你的業務重點。

**這是怎麼做到的？**

我們透過修改 `hugo.toml` 這個網站的「大腦」檔案，來告訴 Hugo 你想展示哪些文章。

1.  **打開 `hugo.toml` 檔案**：
    在 VS Code 的檔案總管中找到 `hugo.toml` 並打開它。

2.  **找到 `[languages]` 區塊**：
    由於你的網站支援多國語言，所以這個設定會放在每個語言各自的參數 (params) 裡面。

    *   **對於英文版 (en)**，你會看到類似這樣的結構：
        ```toml
        [languages.en]
          # ... 其他英文設定 ...
          [languages.en.params]
            description = "..."
            home = "Home"
            [languages.en.params.homepage] # <-- 就是這裡！
              featured_posts = [
                "blog/business-innovation.md",
                "blog/digital-marketing-strategies.md",
                "blog/web-design-principles.md"
              ]
        ```

    *   **對於中文版 (zh)**，結構會是：
        ```toml
        [languages.zh]
          # ... 其他中文設定 ...
          [languages.zh.params]
            description = "..."
            home = "首頁"
            [languages.zh.params.homepage] # <-- 就是這裡！
              featured_posts = [
                "blog/business-innovation.md",
                "blog/digital-marketing-strategies.md",
                "blog/web-design-principles.md"
              ]
        ```

3.  **修改 `featured_posts` 列表**：
    在 `[languages.en.params.homepage]` 和 `[languages.zh.params.homepage]` 下方，你會看到 `featured_posts = [...]` 這一行。

    *   **`featured_posts`**：這是一個列表，裡面列出了你想要在「商業解決方案」區塊顯示的文章路徑。
    *   **路徑格式**：請注意，這裡的文章路徑是**相對於該語言的 `content` 資料夾**。例如，`blog/business-innovation.md` 指的是 `content/zh/blog/business-innovation.md` (如果你在中文版設定) 或 `content/en/blog/business-innovation.md` (如果你在英文版設定)。**不要在路徑前面加上 `zh/` 或 `en/`！**

    **如何修改？**
    *   **新增文章**：如果你想新增一篇文章，只需將其路徑（例如 `"blog/my-new-article.md"`）添加到列表中，並用逗號 `,` 分隔。
    *   **移除文章**：刪除列表中你不想顯示的文章路徑。
    *   **調整順序**：列表中的順序就是文章在首頁上顯示的順序。你可以拖曳調整它們的位置。

**範例：**

假設你想在中文版首頁的「商業解決方案」區塊顯示這三篇文章：
*   `content/zh/blog/business-innovation.md`
*   `content/zh/blog/customer-experience.md`
*   `content/zh/blog/example-post.md` (我們剛剛建立的範例文章)

那麼，你的 `hugo.toml` 中文部分會是這樣：

```toml
[languages.zh.params.homepage]
  featured_posts = [
    "blog/business-innovation.md",
    "blog/customer-experience.md",
    "blog/example-post.md"
  ]
```

修改完成後，記得儲存 `hugo.toml` 檔案，然後重新執行 `hugo server -D`，你就會看到首頁的「商業解決方案」區塊，已經按照你的設定顯示指定的文章了！

這個方法讓你對首頁的內容有了完全的掌控權，非常方便！

---

## 第 6 章：部署上線：讓全世界看到你的網站

你的網站已經在你的電腦上完美運行了，但現在只有你自己看得到。是時候把它推向世界了！這個過程叫做「**部署 (Deployment)**」。

首先，我們要先執行一個指令，把你的網站「烤」成最終的靜態檔案。

在終端機裡（記得先按 `Ctrl+C` 關掉 `hugo server`），輸入：
```bash
hugo
```
就這樣，沒有其他參數。執行完後，你會發現專案裡多了一個 `public/` 資料夾。這個資料夾裡面裝的就是你網站的所有 HTML, CSS, JS 和圖片檔案。它是一個可以獨立運作的完整網站。**我們接下來要做的，就是把這個 `public` 資料夾的內容，上傳到雲端平台。**

我們將介紹三種最受歡迎、對新手最友善、而且有免費方案的平台：**GitHub Pages**, **Netlify**, **Vercel**。

### 6.1 部署平台大比拚

| 平台 | 優點 | 缺點 | 適合誰 |
| :--- | :--- | :--- | :--- |
| **GitHub Pages** | - 完全免費<br>- 與你的程式碼倉庫整合度最高 | - 功能相對陽春<br>- 部署設定稍嫌麻煩 (需要手動設定 GitHub Actions) | 喜歡一切都在 GitHub 生態系裡完成的開發者。 |
| **Netlify** | - 部署超級簡單 (號稱三步驟)<br>- 功能強大 (表單、A/B 測試等)<br>- 自動偵測 Hugo 專案 | - 免費方案有流量和建置時間限制 | **強烈推薦給新手**，無腦部署的最佳選擇。 |
| **Vercel** | - 部署也非常簡單<br>- 效能和速度極佳<br>- 預覽部署功能很強大 | - 免費方案限制與 Netlify 類似<br>- 介面稍微偏向專業開發者 | 追求極致效能，或未來可能使用 Next.js 等框架的人。 |

### 6.2 部署前的準備：將你的網站推上 GitHub

無論你選擇哪個平台，第一步都是把你的網站專案推到 GitHub 上。

1.  **登入 GitHub，建立一個新的倉庫 (Repository)**
    *   點擊右上角的 `+` 號，選擇 `New repository`。
    *   給你的倉庫取個名字，例如 `my-awesome-hugo-site`。
    *   選擇 `Public` (公開)。
    *   **不要**勾選初始化 `README`, `.gitignore` 或 `license` 的選項，因為我們的專案裡已經有這些了。
    *   點擊 `Create repository`。

2.  **將本地專案與遠端倉庫連結**
    GitHub 會給你一些指令。回到你電腦上的 VS Code 終端機，依序執行以下指令（記得把 URL 換成你自己的倉庫 URL）：

    ```bash
    # 第一次初始化 Git (如果你的專案還不是 Git 倉庫)
    git init
    git branch -m main

    # 把所有檔案加入到 Git 的追蹤清單
    git add .

    # 建立你的第一個「快照」，並寫下註解
    git commit -m "Initial commit: My awesome Hugo site"

    # 告訴 Git 你的遠端保險庫在哪裡
    git remote add origin https://github.com/你的使用者名稱/你的倉庫名稱.git

    # 把本地的快照推到 GitHub 保險庫
    git push -u origin main
    ```

    現在，重新整理你的 GitHub 倉庫頁面，你會看到所有專案檔案都已經上傳上去了！

### 6.3 透過 Netlify 部署 (新手首選)

1.  **註冊/登入 Netlify**
    前往 [Netlify 官網](https://www.netlify.com/)，直接用你的 GitHub 帳號授權登入，最方便。

2.  **從 Git 匯入新網站**
    登入後台，點擊 `Add new site > Import an existing project`。

3.  **連接到 GitHub**
    選擇 `GitHub` 作為你的 Git 提供者。它會要求你授權 Netlify 存取你的 GitHub 倉庫。

4.  **選擇你的倉庫**
    從列表中選擇你剛剛建立的那個 `my-awesome-hugo-site` 倉庫。

5.  **設定部署選項**
    這是最神奇的一步。Netlify 通常會**自動偵測**到你這是一個 Hugo 專案，並幫你填好設定：
    *   **Build command**: `hugo`
    *   **Publish directory**: `public`

    你什麼都不用改！直接點擊 `Deploy site`。

6.  **等待部署完成**
    Netlify 會開始工作。它會從 GitHub 拉取你的程式碼，執行 `hugo` 指令，然後把 `public` 資料夾的內容部署到它的全球網路上。這個過程通常需要一兩分鐘。

7.  **網站上線！**
    當你看到「Published」的綠色標籤時，恭喜你！你的網站已經上線了！Netlify 會給你一個隨機的網址，例如 `random-name-12345.netlify.app`。點擊它，就能看到你熱騰騰的網站了！

**自動部署的魔力**：從現在開始，每當你對本地的網站做了修改，然後用 `git push` 把變更推到 GitHub 的 `main` 分支時，Netlify 都會**自動**偵測到，並重新幫你部署一次！你完全不用再手動操作了。

### 6.4 透過 Vercel 部署

Vercel 的流程跟 Netlify 幾乎一模一樣：

1.  **註冊/登入 Vercel** (同樣建議用 GitHub 帳號)。
2.  **匯入專案** (Import Project)。
3.  **選擇你的 Git 倉庫**。
4.  **設定專案**：Vercel 同樣會自動偵測到是 Hugo 專案，並設定好 `Build Command` (`hugo`) 和 `Output Directory` (`public`)。
5.  **點擊 `Deploy`**。
6.  完成！享受你的新網站和 Vercel 提供的網址。

### 6.5 透過 GitHub Pages 部署 (稍進階)

這個方法是利用 GitHub 的一項內建功能，叫做 **GitHub Actions**，來自動化部署流程。

1.  **在你的專案中建立工作流程檔案**
    在 VS Code 中，於你的專案根目錄底下，建立一個新的資料夾結構和檔案：`.github/workflows/hugo.yml`。

2.  **貼上工作流程設定**
    將以下內容完整複製貼到 `hugo.yml` 檔案中：

    ```yaml
    name: Deploy Hugo site to Pages

    on:
      # Runs on pushes targeting the default branch
      push:
        branches:
          - main # 或者你的主要分支名稱

      # Allows you to run this workflow manually from the Actions tab
      workflow_dispatch:

    # Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
    permissions:
      contents: read
      pages: write
      id-token: write

    # Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
    # However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
    concurrency:
      group: "pages"
      cancel-in-progress: false

    jobs:
      # Build job
      build:
        runs-on: ubuntu-latest
        steps:
          - name: Install Hugo CLI
            run: |
              wget -O ${{ runner.temp }}/hugo.deb https://github.com/gohugoio/hugo/releases/download/v0.128.2/hugo_extended_0.128.2_linux-amd64.deb \
              && sudo dpkg -i ${{ runner.temp }}/hugo.deb
          - name: Checkout
            uses: actions/checkout@v4
            with:
              submodules: recursive
              fetch-depth: 0
          - name: Setup Pages
            id: pages
            uses: actions/configure-pages@v5
          - name: Build with Hugo
            run: hugo --baseURL "${{ steps.pages.outputs.base_url }}/"
          - name: Upload artifact
            uses: actions/upload-pages-artifact@v3
            with:
              path: ./public

      # Deployment job
      deploy:
        environment:
          name: github-pages
          url: ${{ steps.deployment.outputs.page_url }}
        runs-on: ubuntu-latest
        needs: build
        steps:
          - name: Deploy to GitHub Pages
            id: deployment
            uses: actions/deploy-pages@v4
    ```
    *注意：* 請檢查 `Install Hugo CLI` 步驟中的 Hugo 版本號，最好將它更新到與你本地 `hugo version` 顯示的版本一致。

3.  **設定 GitHub 倉庫**
    *   回到你的 GitHub 倉庫頁面。
    *   點擊 `Settings > Pages`。
    *   在 `Build and deployment` 下的 `Source`，選擇 `GitHub Actions`。

4.  **推送變更**
    回到 VS Code 終端機，將你剛剛新增的 `hugo.yml` 檔案推送到 GitHub。
    ```bash
    git add .
    git commit -m "Add GitHub Actions workflow for deployment"
    git push
    ```

5.  **觀察 Action 執行**
    去 GitHub 倉庫的 `Actions` 標籤頁，你會看到一個新的工作流程正在執行。點進去看，它會執行 `build` 和 `deploy` 兩個步驟。

6.  **網站上線！**
    當工作流程成功執行完畢後，回到 `Settings > Pages`，你就會看到你的網站已經發布在 `https://你的使用者名稱.github.io/你的倉庫名稱/` 這個網址上了！

---

## 第 7 章：常見問題排除與進階應用

恭喜你！你已經完成了從零到一的整個過程。但在未來的路上，你可能還會遇到一些小石頭。這一章就是你的隨身工具箱。

### 7.1 常見問題 (FAQ)

*   **Q: 我在本地用 `hugo server` 看都好好的，為什麼部署上去圖片或 CSS 樣式就跑掉了？**
    *   **A:** 99% 的可能是 `baseURL` 設定錯誤。打開你的 `hugo.toml`，檢查 `baseURL` 這個參數。
        *   如果你是部署到像 `https://example.com` 這樣的根網域，它應該是 `baseURL = "https://example.com/"`。
        *   如果你是用 GitHub Pages，網址會是 `https://username.github.io/repo-name/`，那麼 `baseURL` 就必須是 `baseURL = "https://username.github.io/repo-name/"`。
        *   Netlify 和 Vercel 通常比較聰明，不太會遇到這個問題，但檢查一下總是好的。

*   **Q: 我 `git push` 之後，線上的網站沒有更新，怎麼辦？**
    *   **A:**
        1.  去 Netlify/Vercel/GitHub Actions 的後台看看最新的部署日誌 (Deploy Log)。通常錯誤訊息都會寫在裡面。
        2.  最常見的錯誤是 Hugo 版本不匹配。可能你本地用的功能比較新，但部署平台上的 Hugo 版本比較舊，導致建置失敗。你可以在 Netlify/Vercel 的環境變數設定中，指定要使用的 Hugo 版本。
        3.  檢查你的 `git status`，確定你真的把所有修改都 `add` 和 `commit` 了。

*   **Q: 我想換個網域名稱，不要用 `netlify.app` 或 `github.io` 這種，可以嗎？**
    *   **A:** 當然可以！你需要去網域註冊商（如 GoDaddy, Namecheap）購買一個你喜歡的網域，然後在 Netlify/Vercel 的後台進行「自訂網域 (Custom Domain)」的設定。它們會引導你如何修改 DNS 紀錄，將你的網域指向它們的伺服器。

### 7.2 推薦資源與持續學習

你的 Hugo 之旅才剛剛開始。這裡有一些能讓你功力大增的寶庫：

*   **[Hugo 官方文件](https://gohugo.io/documentation/)**: 雖然有時有點硬，但它是最權威、最全面的資料來源。
*   **[Hugo Discourse 論壇](https://discourse.gohugo.io/)**: Hugo 的官方社群。遇到解決不了的問題，來這裡發問，通常都會有熱心的大神幫忙。
*   **[parsa 主題文件](https://github.com/themefisher/parsa-hugo)**: 你正在使用的主題的 GitHub 頁面，所有該主題特有的設定和功能都會在這裡說明。

### 結語：你的冒險正要開始

從打開終端機的徬徨，到看見自己親手打造的網站在全世界的網路上線，你已經跨出了一大步。你學會了什麼是靜態網站，安裝了開發工具，駕馭了 Hugo，還征服了雲端部署。

最重要的是，你證明了「架設網站」並不是什麼遙不可及的黑魔法。它需要的是一點點好奇心、一點點耐心，以及一份像這樣的友善指南。

現在，這個網站是你的了。去寫你想寫的，分享你想分享的。讓它成為你在數位世界的名片、你的創作基地、你的思想遊樂園。

旅途愉快，我的朋友！
