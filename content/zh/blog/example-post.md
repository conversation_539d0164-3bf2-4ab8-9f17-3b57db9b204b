---
title: "範例文章：功能展示"
date: 2025-07-06T11:05:00+08:00
image: "images/post/digital-marketing.png"
author: "Gemini 助理"
categories: ["範例", "功能"]
tags: ["shortcodes", "markdown", "mermaid", "youtube"]
draft: false
---

這是一篇範例文章，旨在展示這個 Hugo 主題所提供的一些強大功能。在下面，您將找到如何嵌入各種類型內容的範例，從複雜的圖表到簡單的表格。

## Markdown 表格

表格是組織數據的好方法。它們是使用標準的 Markdown 語法建立的。

| 功能            | 支援程度 | 備註                                  |
|-----------------|----------|---------------------------------------|
| Markdown 表格   | 絕佳     | 易於建立與閱讀。                      |
| Mermaid 圖表    | 原生支援 | 透過程式碼區塊即可使用。              |
| YouTube 影片    | 短代碼   | 使用 `youtube` 標籤即可輕鬆嵌入。     |
| 創作者贊助      | 短代碼   | 使用 `buymeacoffee` 短代碼。          |

---

## Mermaid 圖表

感謝 Hugo 的渲染鉤子 (Render Hooks)，您可以直接在 Markdown 中使用 Mermaid 語法來建立複雜的圖表。只需將其包裝在一個 `mermaid` 程式碼區塊中即可。

```mermaid
graph TD
    A[開始] --> B{這個功能有用嗎？};
    B -- 有用 --> C[實作功能];
    B -- 沒有用 --> D[放棄想法];
    C --> E[完成];
    D --> E;
```

---

## 嵌入 YouTube 影片

嵌入影片非常簡單，只需使用 `youtube` 短代碼並附上影片的 ID 即可。

{{< youtube "q4WKXA-_460" >}}

---

## 支持創作者

如果您覺得這些內容有幫助，可以使用 `buymeacoffee` 短代碼來支持創作者。這是鼓勵更多優質內容的好方法！

{{< buymeacoffee username="themefisher" >}}

希望這篇範例文章能幫助您開始創作屬於自己的精彩內容！
