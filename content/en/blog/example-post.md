---
title: "Example Post: Showcasing Features"
date: 2025-07-06T11:00:00+08:00
image: "images/post/web-design.png"
author: "Gemini Assistant"
categories: ["Examples", "Features"]
tags: ["shortcodes", "markdown", "mermaid", "youtube"]
draft: false
---

This is an example post created to demonstrate some of the powerful features available in this Hugo theme. Below, you'll find examples of how to embed various types of content, from complex diagrams to simple tables.

## Markdown Table

Tables are a great way to organize data. They are created using standard Markdown syntax.

| Feature         | Support Level | Notes                               |
|-----------------|---------------|-------------------------------------|
| Markdown Tables | Excellent     | Easy to create and read.            |
| Mermaid Charts  | Native        | Supported via a code block.         |
| YouTube Videos  | Via Shortcode | Simple to embed with `youtube` tag. |
| Donations       | Via Shortcode | Use the `buymeacoffee` shortcode.   |

---

## Mermaid Diagram

Thanks to <PERSON>'s render hooks, you can create complex diagrams directly in your Markdown using Mermaid syntax. Just wrap it in a `mermaid` code block.

```mermaid
graph TD
    A[Start] --> B{Is it useful?};
    B -- Yes --> C[Implement Feature];
    B -- No --> D[Discard Idea];
    C --> E[Finish];
    D --> E;
```

---

## YouTube Video Embed

Embedding a video is as simple as using the `youtube` shortcode with the video's ID.

{{< youtube "zJliJ_B3e_s" >}}

---

## Support the Creator

If you find this content helpful, you can support the creator using the `buymeacoffee` shortcode. It's a great way to encourage more content!

{{< buymeacoffee username="themefisher" >}}

We hope this example post helps you get started with creating your own amazing content!
