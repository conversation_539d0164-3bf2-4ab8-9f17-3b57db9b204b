{{ $original := .Page.Resources.GetMatch (.Get "src") }}
{{ $alt := .Get "alt" }}
{{ $class := .Get "class" | default "" }}
{{ $width := .Get "width" | default "800" }}
{{ $height := .Get "height" | default "auto" }}
{{ $lazy := .Get "lazy" | default "true" }}

{{ with $original }}
  {{ $webpImage := .Resize (printf "%sx webp q80" $width) }}
  {{ $fallback := .Resize (printf "%sx q85" $width) }}
  
  <picture class="{{ $class }}">
    <source srcset="{{ $webpImage.RelPermalink }}" type="image/webp">
    <source srcset="{{ $fallback.RelPermalink }}" type="image/jpeg">
    
    {{ if eq $lazy "true" }}
      <img 
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjwvc3ZnPg==" 
        data-src="{{ $fallback.RelPermalink }}" 
        alt="{{ $alt }}" 
        width="{{ $webpImage.Width }}" 
        height="{{ if ne $height "auto" }}{{ $height }}{{ else }}{{ $webpImage.Height }}{{ end }}" 
        class="lazy img-fluid {{ $class }}"
        loading="lazy"
      >
    {{ else }}
      <img 
        src="{{ $fallback.RelPermalink }}" 
        alt="{{ $alt }}" 
        width="{{ $webpImage.Width }}" 
        height="{{ if ne $height "auto" }}{{ $height }}{{ else }}{{ $webpImage.Height }}{{ end }}" 
        class="img-fluid {{ $class }}"
      >
    {{ end }}
  </picture>
{{ else }}
  <div class="image-not-found">圖片不存在: {{ .Get "src" }}</div>
{{ end }}
