{{ $src := .Get "src" }}
{{ $alt := .Get "alt" }}
{{ $class := .Get "class" | default "" }}
{{ $width := .Get "width" | default "800" }}
{{ $imgFormat := .Get "format" | default "webp" }}
{{ $quality := .Get "quality" | default "80" }}

{{ $img := resources.Get $src }}
{{ with $img }}
  {{ $options := printf "%sx %s q%s" $width $imgFormat $quality }}
  {{ $resized := .Resize $options }}
  <figure class="figure {{ $class }}">
    <img src="{{ $resized.RelPermalink }}" 
         width="{{ $resized.Width }}" 
         height="{{ $resized.Height }}" 
         alt="{{ $alt }}" 
         loading="lazy"
         class="img-fluid">
    {{ with $.Get "caption" }}
    <figcaption class="figure-caption">{{ . }}</figcaption>
    {{ end }}
  </figure>
{{ else }}
  <div class="alert alert-warning">圖片不存在: {{ $src }}</div>
{{ end }}
