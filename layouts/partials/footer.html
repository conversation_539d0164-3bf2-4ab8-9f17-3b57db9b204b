<footer class="bg-secondary">
  <div class="section">
    <div class="container">
      <div class="row">
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
          <a href="{{site.BaseURL}}">
            {{ partial "image.html" (dict "Src" site.Params.logo "Alt" "Image" "Class" "img-fluid" ) }}
          </a>
        </div>
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
          {{ if .Site.Params.address }}
          <h6>Address</h6>
          <ul class="list-unstyled">
            <li class="font-secondary text-dark">{{site.Params.address | markdownify }}</li>
          </ul>
          {{ end }}
        </div>
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
          {{ if or  .Site.Params.mobile  .Site.Params.email }}         
          <h6>Contact Info</h6>
          <ul class="list-unstyled">
            {{ if .Site.Params.mobile }}
            <li class="font-secondary text-dark">Tel : {{ .Site.Params.mobile | markdownify }}</li>
            {{ end }}
            {{ if .Site.Params.email }}           
            <li class="font-secondary text-dark">Email : {{ .Site.Params.email | markdownify }}</li>
            {{ end }}
          </ul>
          {{ end }}
        </div>
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
          <ul class="list-inline d-inline-block">
            {{ range site.Params.social }}
            <li class="list-inline-item">
              <a href="{{ .url | safeURL }}" class="text-dark" target="_blank" rel="noopener noreferrer" title="{{ .name | title }}">
                {{ if eq .name "github" }}
                  <i class="ti-github"></i>
                {{ else if or (eq .name "linkedin") (eq .name "linkin") }}
                  <i class="ti-linkedin"></i>
                {{ else if eq .name "email" }}
                  <i class="ti-email"></i>
                {{ else if eq .name "twitter" }}
                  <i class="ti-twitter-alt"></i>
                {{ else if eq .name "facebook" }}
                  <i class="ti-facebook"></i>
                {{ else if eq .name "instagram" }}
                  <i class="ti-instagram"></i>
                {{ else }}
                  <i class="ti-link"></i>
                {{ end }}
              </a>
            </li>
            {{ end }}
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="text-center pb-3">
    <p class="mb-0">{{ site.Params.copyright | markdownify }}</p>
  </div>
</footer>


{{ "<!-- JS Plugins -->" | safeHTML }}
{{ range site.Params.plugins.js}}
<script src="{{ .link | absURL }}" defer></script>
{{ end }}

{{ "<!-- Mermaid Script -->" | safeHTML }}
{{ partial "mermaid-script.html" . }}

{{ "<!-- Main Script -->" | safeHTML }}
{{ $script := resources.Get "js/script.js" }}
{{ with $script }}
  {{ $script := $script | minify | fingerprint "sha512" }}
  <script src="{{ $script.Permalink }}" integrity="{{ $script.Data.Integrity }}" defer></script>
{{ end }}

{{ $masonry := resources.Get "js/masonry.min.js" }}
{{ with $masonry }}
  {{ $masonry := $masonry | minify | fingerprint "sha512" }}
  <script src="{{ $masonry.Permalink }}" integrity="{{ $masonry.Data.Integrity }}" defer></script>
{{ end }}

{{ "<!-- 優化腳本 -->" | safeHTML }}
{{ partial "optimize-js.html" . }}
{{ partial "optimize-css.html" . }}

