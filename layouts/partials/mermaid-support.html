<!-- Mermaid 圖表支援 (2025更新到最新版本 11.6.0) -->
<script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化 Mermaid 使用最新設定
    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      flowchart: {
        useMaxWidth: false,
        htmlLabels: true,
        curve: 'basis' // 優化曲線
      },
      securityLevel: 'loose',
      fontFamily: 'Noto Sans TC', // 使用與網站相同的字體
      fontSize: 12, // 調整為更小的字型大小
    });
    
    // 直接搭配以提取並轉換純文本 mermaid 圖表
    document.querySelectorAll('pre').forEach(function(preElement) {
      const codeContent = preElement.textContent.trim();
      
      // 如果是 mermaid 語法，創建新的 div 並渲染
      if (codeContent.startsWith('graph TD') || 
          codeContent.startsWith('graph ') || 
          codeContent.startsWith('flowchart') || 
          codeContent.startsWith('sequenceDiagram') || 
          codeContent.startsWith('classDiagram') || 
          codeContent.startsWith('gantt') ||
          codeContent.startsWith('pie')) {
        
        console.log('Found Mermaid syntax');
        
        // 創建 mermaid 容器
        const mermaidDiv = document.createElement('div');
        mermaidDiv.className = 'mermaid';
        mermaidDiv.textContent = codeContent;
        
        // 插入新容器並隱藏原代碼區塊
        preElement.parentNode.insertBefore(mermaidDiv, preElement);
        preElement.style.display = 'none';
        
        // 嘗試渲染
        try {
          mermaid.init(undefined, mermaidDiv);
        } catch (e) {
          console.error('Mermaid rendering error:', e);
          // 如果失敗，還原原始區塊
          preElement.style.display = 'block';
          mermaidDiv.remove();
        }
      }
    });
  });
</script>
