<!-- custom-opengraph.html -->
<!-- 增強的 Open Graph 標籤 - 確保社交媒體縮圖正確顯示 -->
<meta property="og:title" content="{{ .Title }}" />
<meta property="og:description" content="{{ with .Description }}{{ . }}{{ else }}{{ if .IsPage }}{{ .Summary }}{{ else }}{{ with .Site.Params.description }}{{ . }}{{ end }}{{ end }}{{ end }}" />
<meta property="og:type" content="{{ if .IsPage }}article{{ else }}website{{ end }}" />
<meta property="og:url" content="{{ .Permalink }}" />
<meta property="og:site_name" content="{{ .Site.Title }}" />

<!-- 確保有圖片 - 優先順序：頁面 images > 頁面 image > 網站默認圖片 -->
{{ $ogImage := "" }}
{{ with .Params.images }}{{ $ogImage = index . 0 }}{{ end }}
{{ if not $ogImage }}
  {{ with .Params.image }}{{ $ogImage = . }}{{ end }}
{{ end }}
{{ if not $ogImage }}
  {{ with .Site.Params.images }}{{ $ogImage = index . 0 }}{{ end }}
{{ end }}

<!-- 檢查並確保圖片 URL 是絕對路徑 -->
{{ $isAbsoluteUrl := false }}
{{ if and $ogImage (hasPrefix $ogImage "http") }}
  {{ $isAbsoluteUrl = true }}
{{ end }}

{{ if $ogImage }}
  {{ if not $isAbsoluteUrl }}
    {{ $ogImage = $ogImage | absURL }}
  {{ end }}
  <meta property="og:image" content="{{ $ogImage }}" />
  <meta property="og:image:secure_url" content="{{ $ogImage }}" />
  <meta name="twitter:image" content="{{ $ogImage }}" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
{{ end }}

<!-- Twitter Card 標籤 -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{{ .Title }}" />
<meta name="twitter:description" content="{{ with .Description }}{{ . }}{{ else }}{{ if .IsPage }}{{ .Summary }}{{ else }}{{ with .Site.Params.description }}{{ . }}{{ end }}{{ end }}{{ end }}" />

<!-- 額外的社交媒體標籤 -->
{{ if .IsPage }}
<meta property="article:published_time" content="{{ .Date.Format "2006-01-02T15:04:05-07:00" | safeHTML }}" />
{{ end }}
