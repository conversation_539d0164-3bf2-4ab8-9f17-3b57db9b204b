<!-- 自定義頭部內容 -->

<!-- Mermaid 圖表支持 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.css">
<script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  mermaid.initialize({
    startOnLoad: true,
    theme: 'default',
    fontFamily: 'Noto Sans TC, sans-serif',
    fontSize: '12px',
    flowchart: {
      useMaxWidth: true,
      htmlLabels: true,
      rankSpacing: 30,
      nodeSpacing: 40,
      curve: 'linear',
      wrap: true
    },
    themeCSS: `
      .node rect, .node circle, .node ellipse, .node polygon, .node path {
        fill: #f9f9f9;
        stroke: #999;
        stroke-width: 1px;
      }
      .edgePath .path {
        stroke: #999;
        stroke-width: 1.5px;
      }
      .edgeLabel {
        background-color: white;
        font-size: 10px;
        padding: 2px 4px;
      }
      .label {
        font-size: 12px;
      }
    `
  });
});
</script>

<style>
/* Mermaid 圖表樣式 */
.mermaid-wrapper {
  margin: 1rem auto;
  text-align: center;
  max-width: 100%;
}
.mermaid {
  display: inline-block;
  width: auto;
  max-width: 100%;
}
.mermaid svg {
  max-width: 100%;
  height: auto !important;
  font-size: 12px;
}
</style>
