{{ $js := resources.Get "js/optimize.js" }}
{{ with $js }}
  {{ $opts := dict "targetPath" "js/optimize.min.js" "minify" true }}
  {{ if hugo.IsProduction }}
    {{ $optimizeJS := $js | js.Build $opts | fingerprint "sha512" }}
    <script src="{{ $optimizeJS.RelPermalink }}" integrity="{{ $optimizeJS.Data.Integrity }}" defer></script>
  {{ else }}
    {{ $optimizeJS := $js | js.Build $opts }}
    <script src="{{ $optimizeJS.RelPermalink }}" defer></script>
  {{ end }}
{{ end }}
