<!-- 優化 JavaScript 載入 -->
{{ $scripts := slice }}

<!-- 只在需要的頁面載入特定腳本 -->
{{ if .IsHome }}
  {{ $homeJS := resources.Get "js/home.js" }}
  {{ with $homeJS }}
    {{ $scripts = $scripts | append (. | minify | fingerprint "sha512") }}
  {{ end }}
{{ end }}

{{ if eq .Layout "contact" }}
  {{ $contactJS := resources.Get "js/contact.js" }}
  {{ with $contactJS }}
    {{ $scripts = $scripts | append (. | minify | fingerprint "sha512") }}
  {{ end }}
{{ end }}

<!-- 對每個腳本進行處理 -->
{{ range $scripts }}
  <script src="{{ .RelPermalink }}" integrity="{{ .Data.Integrity }}" defer></script>
{{ end }}

<!-- 延遲載入非關鍵第三方腳本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(function() {
    // 延遲載入非關鍵腳本
    const thirdPartyScripts = [];
    
    thirdPartyScripts.forEach(function(scriptSrc) {
      const script = document.createElement('script');
      script.src = scriptSrc;
      script.async = true;
      document.body.appendChild(script);
    });
  }, 3000); // 延遲 3 秒載入非關鍵腳本
});
</script>
