<!-- social-media.html - 專門優化社交媒體分享與爬蟲 -->
<!-- 確保所有社交媒體平台可以正確抓取內容 -->

<!-- 基本 Open Graph 標籤 -->
<meta property="og:locale" content="zh_TW" />
<meta property="og:type" content="{{ if .IsPage }}article{{ else }}website{{ end }}" />
<meta property="og:title" content="{{ .Title }}" />
<meta property="og:description" content="{{ with .Description }}{{ . }}{{ else }}{{ if .IsPage }}{{ .Summary }}{{ else }}{{ with site.Params.description }}{{ . }}{{ end }}{{ end }}{{ end }}" />
<meta property="og:url" content="{{ .Permalink }}" />
<meta property="og:site_name" content="{{ .Site.Title }}" />

<!-- Twitter Card 標籤 -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{{ .Title }}" />
<meta name="twitter:description" content="{{ with .Description }}{{ . }}{{ else }}{{ if .IsPage }}{{ .Summary }}{{ else }}{{ with site.Params.description }}{{ . }}{{ end }}{{ end }}{{ end }}" />

<!-- 優先使用頁面指定的圖片 -->
{{ $ogImage := "" }}
{{ if .Params.images }}
  {{ $ogImage = index .Params.images 0 | absURL }}
{{ else if .Params.image }}
  {{ $ogImage = .Params.image | absURL }}
{{ else if .Site.Params.images }}
  {{ $ogImage = index .Site.Params.images 0 | absURL }}
{{ end }}

{{ if $ogImage }}
<!-- Facebook 和通用 Open Graph 圖片標籤 -->
<meta property="og:image" content="{{ $ogImage }}" />
<meta property="og:image:secure_url" content="{{ $ogImage }}" />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
<meta property="og:image:alt" content="{{ .Title }}" />

<!-- Twitter 圖片標籤 -->
<meta name="twitter:image" content="{{ $ogImage }}" />
{{ end }}

<!-- 文章專用標籤 -->
{{ if .IsPage }}
<meta property="article:published_time" content="{{ .Date.Format "2006-01-02T15:04:05-07:00" }}" />
<meta property="article:modified_time" content="{{ .Lastmod.Format "2006-01-02T15:04:05-07:00" }}" />
{{ with .Params.tags }}
  {{ range first 6 . }}
  <meta property="article:tag" content="{{ . }}" />
  {{ end }}
{{ end }}

<!-- 確保 Facebook 正確識別內容類型 -->
<meta property="article:section" content="{{ if .Params.category }}{{ index .Params.category 0 }}{{ else }}{{ if .Params.categories }}{{ index .Params.categories 0 }}{{ else }}Uncategorized{{ end }}{{ end }}" />
{{ end }}

<!-- LinkedIn 專用標籤 -->
<meta name="author" content="{{ .Site.Params.author }}" />

<!-- 強制清晰的內容類型標頭，避免社交媒體爬蟲混淆 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

<!-- 防止快取問題 -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
<meta http-equiv="Pragma" content="no-cache" />
<meta http-equiv="Expires" content="0" />
