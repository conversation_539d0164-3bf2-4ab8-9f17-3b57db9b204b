<head>
  <meta charset="utf-8">
  <title>{{ with .Title }}{{ . }}{{ else }}{{ with site.Title }}{{ . }}{{ end }}{{ end }}</title>

  {{ "<!-- mobile responsive meta -->" | safeHTML }}
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="description" content="{{ with .Description }}{{ . }}{{ else }}{{ with site.Params.description }}{{ . }}{{ end }}{{ end }}">
  {{ with site.Params.author }}<meta name="author" content="{{ . }}">{{ end }}
  <!-- theme meta -->
  <meta name="theme-name" content="parsa-hugo" />
  {{ hugo.Generator }}

  {{ "<!-- plugins -->" | safeHTML }}
  {{ range site.Params.plugins.css }}
  <link rel="stylesheet" href="{{ .link | absURL }} ">
  {{ end }}

  {{ "<!-- Main Stylesheet -->" | safeHTML }}
  {{ $styles := resources.Get "css/style.css" | minify }}
  <link rel="stylesheet" href="{{ $styles.Permalink }}" media="screen">
  
  {{ "<!-- Custom CSS -->" | safeHTML }}
  {{ $custom := resources.Get "css/custom.css" }}
  {{ with $custom }}
    {{ $custom := $custom | minify }}
    <link rel="stylesheet" href="{{ $custom.Permalink }}" integrity="{{ $custom.Data.Integrity }}">
  {{ end }}
  
  {{ "<!-- Syntax Highlighting CSS -->" | safeHTML }}
  {{ $syntaxCSS := resources.Get "css/syntax-highlight.css" }}
  {{ with $syntaxCSS }}
    {{ $syntaxCSS := $syntaxCSS | minify }}
    <link rel="stylesheet" href="{{ $syntaxCSS.Permalink }}" integrity="{{ $syntaxCSS.Data.Integrity }}">
  {{ end }}
  
  {{ "<!-- Mermaid Custom CSS -->" | safeHTML }}
  {{ $mermaidCSS := resources.Get "css/mermaid-custom.css" }}
  {{ with $mermaidCSS }}
    {{ $mermaidCSS := $mermaidCSS | minify }}
    <link rel="stylesheet" href="{{ $mermaidCSS.Permalink }}" integrity="{{ $mermaidCSS.Data.Integrity }}">
  {{ end }}
  
  {{ "<!-- Font Custom CSS - 繁體中文字體優化 -->" | safeHTML }}
  {{ $fontCSS := resources.Get "css/font-custom.css" }}
  {{ with $fontCSS }}
    {{ $fontCSS := $fontCSS | minify }}
    <link rel="stylesheet" href="{{ $fontCSS.Permalink }}" integrity="{{ $fontCSS.Data.Integrity }}">
  {{ end }}
  
  {{ "<!-- Home Page Custom CSS -->" | safeHTML }}
  {{ $homeCSS := resources.Get "css/home-custom.css" }}
  {{ with $homeCSS }}
    {{ $homeCSS := $homeCSS | minify }}
    <link rel="stylesheet" href="{{ $homeCSS.Permalink }}" integrity="{{ $homeCSS.Data.Integrity }}">
  {{ end }}
  
  {{ "<!-- Google Fonts - 加載字體但延遲渲染，優化效能 -->" | safeHTML }}
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&family=Noto+Serif+TC:wght@400;600;700&family=Source+Code+Pro:wght@400;600&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
  <noscript>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&family=Noto+Serif+TC:wght@400;600;700&family=Source+Code+Pro:wght@400;600&display=swap" rel="stylesheet">
  </noscript>

  {{ "<!--Favicon-->" | safeHTML }}
  <link rel="shortcut icon" href="{{ `images/favicon.png` | absURL }} " type="image/x-icon">
  <link rel="icon" href="{{ `images/favicon.png` | absURL }} " type="image/x-icon">
  
  {{ "<!-- Code Copy JS -->" | safeHTML }}
  {{ $codeCopy := resources.Get "js/code-copy.js" }}
  {{ with $codeCopy }}
    {{ $codeCopy := $codeCopy | minify }}
    <script src="{{ $codeCopy.Permalink }}"></script>
  {{ end }}
  
  {{ "<!-- Mermaid Diagram Support -->" | safeHTML }}
  {{ partial "mermaid-support.html" . }}
  
  {{ "<!-- 增強的社交媒體標籤，專門優化 Facebook 和 LinkedIn 爬蟲 -->" | safeHTML }}
  {{ partial "social-media.html" . }}
  
  {{ "<!-- Google AdSense -->" | safeHTML }}
  {{ if site.Params.adsense.enabled }}
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client={{ site.Params.adsense.client }}" crossorigin="anonymous"></script>
  {{ end }}
  
  {{ "<!-- 分析追蹤代碼 -->" | safeHTML }}
  {{ template "_internal/google_analytics.html" . }}
  {{ partial "facebook-pixel.html" . }}
</head>
