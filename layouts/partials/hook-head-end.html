<!-- 檢查是否有 Mermaid 圖表 -->
{{ if .Store.Get "hasMermaid" }}
  <!-- 載入 Mermaid JS -->
  <script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化 Mermaid 使用最新設定
      mermaid.initialize({
        startOnLoad: true,
        theme: 'default',
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true,
          curve: 'basis', // 優化曲線
          ranker: 'tight-tree', // 更緊湊的圖表佈局
          wrap: true // 長文字自動換行
        },
        securityLevel: 'loose',
        fontFamily: 'Noto Sans TC', // 使用與網站相同的字體
        fontSize: 12, // 調整為更小的字型大小
        maxTextSize: 12, // 限制最大文字尺寸
        gantt: { fontSize: 12 },
        sequence: { fontSize: 12 },
        journey: { fontSize: 12 },
        er: { fontSize: 12 },
        pie: { fontSize: 12 }
      });
    });
  </script>
  <style>
    .mermaid {
      background-color: transparent;
      text-align: center;
      font-size: 12px;
      width: 100%;
    }
    .mermaid svg {
      max-width: 100%;
      height: auto;
      max-height: 500px;
    }
    .mermaid .label {
      font-size: 12px;
    }
    .mermaid .node rect, 
    .mermaid .node circle, 
    .mermaid .node ellipse, 
    .mermaid .node polygon, 
    .mermaid .node path {
      fill: #f9f9f9;
      stroke: #999;
      stroke-width: 1px;
    }
    .mermaid .edgePath .path {
      stroke: #999;
      stroke-width: 1.5px;
    }
    .mermaid .edgeLabel {
      background-color: white;
      font-size: 10px;
    }
    .mermaid .cluster rect {
      fill: #f9f9f9;
      stroke: #999;
      stroke-width: 1px;
    }
  </style>
{{ end }}
