<header class="navigation">
  <nav class="navbar navbar-expand-lg navbar-light">
    <a class="navbar-brand" href="{{site.BaseURL | relLangURL}}">
      {{ partial "image.html" (dict "Src" .Site.Params.logo "Alt" "Image" "Class" "img-fluid" ) }}
    </a>
    <button class="navbar-toggler border-0" type="button" data-toggle="collapse" data-target="#navogation"
      aria-controls="navogation" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse text-center" id="navogation">
      <ul class="navbar-nav ml-auto">
        {{ range site.Menus.main }}
        {{ if .HasChildren }}
        <li class="nav-item dropdown">
          <a class="nav-link text-uppercase text-dark dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-haspopup="true"
            aria-expanded="false">
            {{ .Name }}
          </a>
          <div class="dropdown-menu" >
            {{ range .Children }}
            <a class="dropdown-item" href="{{ .URL | absURL }}">{{ .Name }}</a>
            {{ end }}
          </div>
        </li>
        {{ else }}
        <li class="nav-item">
          <a class="nav-link text-uppercase text-dark" href="{{ .URL | absURL }}">{{ .Name }}</a>
        </li>
        {{ end }}
        {{ end }}
      </ul>
      
      <!-- Language Switcher -->
      {{ partial "language-switcher.html" . }}
      
      {{ if site.Params.search }}
      {{ "<!-- search -->" | safeHTML }}
      <form class="form-inline position-relative ml-lg-4" action="{{ site.BaseURL }}/search/">
        <input class="form-control px-0 w-100" type="search" placeholder="Search" id="search-query" name="s">
        <button class="search-icon" type="submit"><i class="ti-search text-dark"></i></button>
      </form>
      {{ end }}
    </div>
  </nav>
</header>
