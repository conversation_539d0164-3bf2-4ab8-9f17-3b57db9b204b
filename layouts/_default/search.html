{{ define "main" }}

<!-- page-title -->
<section class="section bg-secondary">
  <div class="container">
    <div class="row">
      <div class="col-lg-12">
        <h4>{{.Title}}</h4>
      </div>
    </div>
  </div>
</section>
<!-- /page-title -->

<!-- search result -->
<section class="section">
  <div class="container">
    <div class="row">
      <div class="col-lg-10 mx-auto">
        <!-- Search Form -->
        <div class="search-form mb-5">
          <form action="{{ "search" | relURL }}">
            <div class="input-group">
              <input type="search" class="form-control" id="search-query" name="s" placeholder="Enter search keywords...">
              <div class="input-group-append">
                <button class="btn btn-primary" type="submit">Search</button>
              </div>
            </div>
          </form>
        </div>
        <!-- /Search Form -->
        
        <div id="search-results"></div>
        <script id="search-result-template" type="text/x-js-template">
          <ul class="search-result list-unstyled" id="summary-${key}">
            <li class="border-bottom mb-4 pb-3">
              <h4><a class="text-dark" href="${link}">${title}</a></h4>
              <p>${snippet}</p>
              ${ isset tags }<p>Tags: ${tags}</p>${ end }
              ${ isset categories }<p>Categories: ${categories}</p>${ end }
            </li>
          </ul>
        </script>
      </div>
    </div>
  </div>
</section>
<!-- /search result -->

{{ "<!-- Search index -->" | safeHTML }}
<script>
  var indexURL = {{"index.json" | absURL}}
</script>

<!-- Load custom search script -->
<script src="{{ "js/search.js" | relURL }}"></script>

{{ end }}
