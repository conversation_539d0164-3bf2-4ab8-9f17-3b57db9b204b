{{ define "main" }}

<!-- page title -->
<section class="section bg-secondary">
  <div class="container">
    <div class="row">
      <div class="col-lg-12">
        <h4>{{ .Title }}</h4>
      </div>
    </div>
  </div>
</section>
<!-- /page title -->

<!-- archives -->
<section class="section">
  <div class="container">
    <div class="row">
      <div class="col-lg-10 mx-auto">
        <div class="archives-container">
          <!-- 按年份分組顯示文章 -->
          {{ range .Site.RegularPages.GroupByDate "2006" }}
          <div class="archive-year-section mb-5">
            <h2 class="mb-4 archive-year">{{ .Key }}</h2>
            
            <!-- 每年的文章列表 -->
            {{ range .Pages.GroupByDate "January" }}
            <div class="archive-month-section mb-4">
              <h4 class="archive-month">{{ .Key }}</h4>
              <ul class="list-unstyled">
                {{ range .Pages }}
                <li class="archive-item mb-3">
                  <div class="d-flex align-items-center">
                    <span class="archive-date text-muted mr-3">{{ .Date.Format "02" }}</span>
                    <a href="{{ .Permalink }}" class="archive-title">{{ .Title }}</a>
                  </div>
                </li>
                {{ end }}
              </ul>
            </div>
            {{ end }}
          </div>
          {{ end }}
        </div>
      </div>
    </div>
  </div>
</section>
<!-- /archives -->

<!-- 添加一些自定義 CSS 以優化歸檔頁面外觀 -->
<style>
  .archive-year {
    border-bottom: 2px solid #f5f5f5;
    padding-bottom: 10px;
    color: #333;
  }
  
  .archive-month {
    font-size: 1.2rem;
    color: #555;
    margin-left: 15px;
    margin-bottom: 15px;
    border-left: 3px solid #007bff;
    padding-left: 10px;
  }
  
  .archive-item {
    margin-left: 30px;
  }
  
  .archive-date {
    min-width: 30px;
    display: inline-block;
  }
  
  .archive-title {
    color: #333;
    text-decoration: none;
    transition: color 0.3s;
  }
  
  .archive-title:hover {
    color: #007bff;
    text-decoration: none;
  }
</style>

{{ end }}
