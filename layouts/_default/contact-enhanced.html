{{ define "main" }}

<section class="section">
  <div class="container">
    <div class="row">
      <div class="col-lg-12">
        <h2 class="mb-4">{{.Title }}</h2>
        {{ partial "image.html" (dict "Src" .Params.Image "Alt" "Image" "Class" "img-fluid w-100 mb-4" ) }}
        {{ .Content }}
        
        <!-- Enhanced Netlify Forms 聯絡表單 -->
        <form name="contact" 
              netlify 
              netlify-honeypot="bot-field" 
              class="row mt-5" 
              method="POST"
              data-netlify-recaptcha="true"
              action="/success/">
          
          <!-- 隱藏的蜜罐欄位防止垃圾郵件 -->
          <input type="hidden" name="bot-field" />
          <input type="hidden" name="form-name" value="contact" />
          
          <!-- 表單提交時間戳 -->
          <input type="hidden" name="submission-time" id="submission-time" />
          
          <div class="col-lg-6">
            <label for="name" class="form-label">{{ i18n "contact.your_name" | default "Your Name" }} *</label>
            <input type="text" 
                   class="form-control mb-4" 
                   name="name" 
                   id="name" 
                   placeholder="{{ i18n "contact.your_name" | default "Your Name" }}" 
                   required
                   minlength="2"
                   maxlength="100">
          </div>
          
          <div class="col-lg-6">
            <label for="email" class="form-label">{{ i18n "contact.your_email" | default "Your Email" }} *</label>
            <input type="email" 
                   class="form-control mb-4" 
                   name="email" 
                   id="email" 
                   placeholder="{{ i18n "contact.your_email" | default "Your Email" }}" 
                   required
                   pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
          </div>
          
          <div class="col-lg-6">
            <label for="company" class="form-label">{{ i18n "contact.company" | default "Company" }}</label>
            <input type="text" 
                   class="form-control mb-4" 
                   name="company" 
                   id="company" 
                   placeholder="{{ i18n "contact.company" | default "Company (Optional)" }}"
                   maxlength="100">
          </div>
          
          <div class="col-lg-6">
            <label for="phone" class="form-label">{{ i18n "contact.phone" | default "Phone" }}</label>
            <input type="tel" 
                   class="form-control mb-4" 
                   name="phone" 
                   id="phone" 
                   placeholder="{{ i18n "contact.phone" | default "Phone (Optional)" }}"
                   pattern="[0-9+\-\s\(\)]{8,20}">
          </div>
          
          <div class="col-12">
            <label for="subject" class="form-label">{{ i18n "contact.subject" | default "Subject" }} *</label>
            <select class="form-control mb-4" name="subject" id="subject" required>
              <option value="">{{ i18n "contact.select_subject" | default "Select a subject..." }}</option>
              <option value="Business Strategy Consultation">{{ i18n "contact.business_strategy" | default "Business Strategy Consultation" }}</option>
              <option value="Digital Marketing Services">{{ i18n "contact.digital_marketing" | default "Digital Marketing Services" }}</option>
              <option value="Website Development">{{ i18n "contact.website_development" | default "Website Development" }}</option>
              <option value="Partnership Opportunities">{{ i18n "contact.partnership" | default "Partnership Opportunities" }}</option>
              <option value="Career Inquiries">{{ i18n "contact.career" | default "Career Inquiries" }}</option>
              <option value="Other">{{ i18n "contact.other" | default "Other" }}</option>
            </select>
          </div>
          
          <div class="col-12">
            <label for="message" class="form-label">{{ i18n "contact.message" | default "Message" }} *</label>
            <textarea name="message" 
                      id="message" 
                      class="form-control mb-4" 
                      placeholder="{{ i18n "contact.message_placeholder" | default "Please describe your inquiry in detail..." }}" 
                      rows="6" 
                      required
                      minlength="10"
                      maxlength="2000"></textarea>
            <small class="form-text text-muted">
              <span id="char-count">0</span>/2000 {{ i18n "contact.characters" | default "characters" }}
            </small>
          </div>
          
          <!-- reCAPTCHA -->
          <div class="col-12 mb-4">
            <div data-netlify-recaptcha="true"></div>
          </div>
          
          <!-- 隱私政策同意 -->
          <div class="col-12 mb-4">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="privacy-consent" id="privacy-consent" required>
              <label class="form-check-label" for="privacy-consent">
                {{ i18n "contact.privacy_consent" | default "I agree to the privacy policy and terms of service" }} *
              </label>
            </div>
          </div>
          
          <div class="col-12">
            <button type="submit" class="btn btn-primary btn-lg" id="submit-btn">
              <span class="btn-text">{{ i18n "contact.send_message" | default "Send Message" }}</span>
              <span class="btn-loading d-none">
                <i class="fas fa-spinner fa-spin mr-2"></i>{{ i18n "contact.sending" | default "Sending..." }}
              </span>
            </button>
          </div>
        </form>
        
        <!-- 聯絡資訊 -->
        <div class="row mt-5">
          <div class="col-lg-4 mb-4">
            <div class="contact-info">
              <h5><i class="ti-email mr-2"></i>{{ i18n "common.email" | default "Email" }}</h5>
              <p><a href="mailto:{{ site.Params.email }}">{{ site.Params.email }}</a></p>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="contact-info">
              <h5><i class="ti-mobile mr-2"></i>{{ i18n "common.phone" | default "Phone" }}</h5>
              <p><a href="tel:{{ site.Params.mobile }}">{{ site.Params.mobile }}</a></p>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="contact-info">
              <h5><i class="ti-location-pin mr-2"></i>{{ i18n "common.address" | default "Address" }}</h5>
              <p>{{ site.Params.address }}</p>
            </div>
          </div>
        </div>
        
        <!-- 回應時間說明 -->
        <div class="alert alert-info mt-4">
          <h6><i class="ti-info mr-2"></i>{{ i18n "contact.response_time_title" | default "Response Time" }}</h6>
          <p class="mb-0">{{ i18n "contact.response_time_text" | default "We typically respond to all inquiries within 24 hours during business days. For urgent matters, please call us directly." }}</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Enhanced JavaScript for form functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[name="contact"]');
    const submitBtn = document.getElementById('submit-btn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    const messageTextarea = document.getElementById('message');
    const charCount = document.getElementById('char-count');
    const submissionTimeInput = document.getElementById('submission-time');
    
    // Set submission timestamp
    submissionTimeInput.value = new Date().toISOString();
    
    // Character counter
    messageTextarea.addEventListener('input', function() {
        const count = this.value.length;
        charCount.textContent = count;
        
        if (count > 1900) {
            charCount.style.color = '#dc3545';
        } else if (count > 1500) {
            charCount.style.color = '#ffc107';
        } else {
            charCount.style.color = '#6c757d';
        }
    });
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.disabled = true;
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        
        // Basic client-side validation
        const email = document.getElementById('email').value;
        const message = document.getElementById('message').value;
        
        if (!email.includes('@') || message.length < 10) {
            e.preventDefault();
            alert('Please fill in all required fields correctly.');
            resetSubmitButton();
            return;
        }
        
        // If validation passes, form will submit normally
        // Netlify will handle the actual submission
    });
    
    function resetSubmitButton() {
        submitBtn.disabled = false;
        btnText.classList.remove('d-none');
        btnLoading.classList.add('d-none');
    }
    
    // Reset button state if form submission fails
    window.addEventListener('pageshow', function() {
        resetSubmitButton();
    });
});
</script>

{{partial "instafeed.html" . }}

{{ end }}