{{ define "main" }}

<section class="section">
  <div class="container">
    <div class="row">
      <div class="col-lg-12">
        <h2 class="mb-4">{{.Title }}</h2>
        {{ partial "image.html" (dict "Src" .Params.Image "Alt" "Image" "Class" "img-fluid w-100 mb-4" ) }}
        {{ .Content }}
        
        <!-- Netlify Forms 聯絡表單 -->
        <form name="contact" netlify netlify-honeypot="bot-field" class="row mt-5" method="POST">
          <!-- 隱藏的蜜罐欄位防止垃圾郵件 -->
          <input type="hidden" name="bot-field" />
          <input type="hidden" name="form-name" value="contact" />
          
          <div class="col-lg-6">
            <input type="text" class="form-control mb-4" name="name" id="name" placeholder="{{ i18n "contact.your_name" | default "Your Name" }}" required>
          </div>
          <div class="col-lg-6">
            <input type="email" class="form-control mb-4" name="email" id="email" placeholder="{{ i18n "contact.your_email" | default "Your Email" }}" required>
          </div>
          <div class="col-12">
            <input type="text" class="form-control mb-4" name="subject" id="subject" placeholder="{{ i18n "contact.subject" | default "Subject" }}" required>
          </div>
          <div class="col-12">
            <textarea name="message" id="message" class="form-control mb-4" placeholder="{{ i18n "contact.message" | default "Message..." }}" rows="6" required></textarea>
          </div>
          <div class="col-12">
            <button type="submit" class="btn btn-primary">{{ i18n "contact.send_message" | default "Send Message" }}</button>
          </div>
        </form>
        
        <!-- 聯絡資訊 -->
        <div class="row mt-5">
          <div class="col-lg-4 mb-4">
            <div class="contact-info">
              <h5><i class="ti-email mr-2"></i>{{ i18n "common.email" | default "Email" }}</h5>
              <p>{{ site.Params.email }}</p>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="contact-info">
              <h5><i class="ti-mobile mr-2"></i>{{ i18n "common.phone" | default "Phone" }}</h5>
              <p>{{ site.Params.mobile }}</p>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="contact-info">
              <h5><i class="ti-location-pin mr-2"></i>{{ i18n "common.address" | default "Address" }}</h5>
              <p>{{ site.Params.address }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{{partial "instafeed.html" . }}

{{ end }}