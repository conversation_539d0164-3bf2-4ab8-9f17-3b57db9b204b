{{ define "main" }}

<section class="section">
  <div class="container">
    <div class="row">
      <div class="col-lg-12">
        <h2 class="mb-4">{{.Title }}</h2>
        {{ partial "image.html" (dict "Src" .Params.Image "Alt" "Image" "Class" "img-fluid w-100 mb-4" ) }}
        {{ .Content }}
        
        <!-- Google Forms Email Collection -->
        <div class="row mt-5">
          <div class="col-lg-8 mx-auto">
            <div class="card shadow-sm">
              <div class="card-body p-4">
                <h4 class="card-title text-center mb-4">
                  {{ i18n "contact.email_signup" | default "訂閱我們的最新消息" }}
                </h4>
                
                <form id="google-email-form" 
                      method="POST"
                      target="hidden_iframe"
                      onsubmit="submitEmailForm()">
                  
                  <div class="form-group">
                    <label for="email" class="form-label">
                      {{ i18n "contact.your_email" | default "Your Email" }} *
                    </label>
                    <input type="email" 
                           class="form-control form-control-lg" 
                           name="{{ site.Params.googleForms.emailFieldName | default "entry.1234567890" }}" 
                           id="email" 
                           placeholder="{{ i18n "contact.email_placeholder" | default "請輸入您的 Email 地址" }}" 
                           required>
                    <small class="form-text text-muted">
                      {{ i18n "contact.email_help" | default "我們會在24小時內回覆您，絕不會將您的信箱分享給第三方" }}
                    </small>
                  </div>
                  
                  <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg px-5" id="submit-btn">
                      <span class="btn-text">{{ i18n "contact.submit_email" | default "提交 Email" }}</span>
                      <span class="btn-loading d-none">
                        <i class="fas fa-spinner fa-spin mr-2"></i>{{ i18n "contact.submitting" | default "提交中..." }}
                      </span>
                    </button>
                  </div>
                </form>
                
                <!-- 隱藏的 iframe 用於 Google Forms 提交 -->
                <iframe name="hidden_iframe" id="hidden_iframe" style="display:none;" onload="emailFormSubmitted()"></iframe>
                
                <!-- 成功訊息 -->
                <div id="success-message" class="alert alert-success mt-4 d-none">
                  <h5><i class="fas fa-check-circle mr-2"></i>{{ i18n "contact.success_title" | default "Email 提交成功！" }}</h5>
                  <p class="mb-0">{{ i18n "contact.success_message" | default "感謝您提供 Email，我們會盡快與您聯繫。" }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 聯絡資訊 -->
        <div class="row mt-5">
          <div class="col-lg-4 mb-4">
            <div class="contact-info text-center">
              <h5><i class="ti-email mr-2"></i>{{ i18n "common.email" | default "Email" }}</h5>
              <p><a href="mailto:{{ site.Params.email }}">{{ site.Params.email }}</a></p>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="contact-info text-center">
              <h5><i class="ti-mobile mr-2"></i>{{ i18n "common.phone" | default "Phone" }}</h5>
              <p><a href="tel:{{ site.Params.mobile }}">{{ site.Params.mobile }}</a></p>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="contact-info text-center">
              <h5><i class="ti-location-pin mr-2"></i>{{ i18n "common.address" | default "Address" }}</h5>
              <p>{{ site.Params.address }}</p>
            </div>
          </div>
        </div>
        
        <!-- 說明區塊 -->
        <div class="alert alert-info mt-4">
          <h6><i class="ti-info mr-2"></i>{{ i18n "contact.how_to_setup" | default "如何設定 Google 表單" }}</h6>
          <div class="small">
            <p><strong>{{ i18n "contact.step1" | default "步驟 1：" }}</strong> {{ i18n "contact.step1_desc" | default "建立 Google 表單，新增一個「Email」欄位（短答案，必填）" }}</p>
            <p><strong>{{ i18n "contact.step2" | default "步驟 2：" }}</strong> {{ i18n "contact.step2_desc" | default "取得表單的公開連結" }}</p>
            <p><strong>{{ i18n "contact.step3" | default "步驟 3：" }}</strong> {{ i18n "contact.step3_desc" | default "檢查表單原始碼，找到 action URL 和 input name（如：entry.1234567890）" }}</p>
            <p><strong>{{ i18n "contact.step4" | default "步驟 4：" }}</strong> {{ i18n "contact.step4_desc" | default "在 hugo.toml 中更新 googleForms.actionURL 和 googleForms.emailFieldName" }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Google Forms JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('google-email-form');
    const submitBtn = document.getElementById('submit-btn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    const successMessage = document.getElementById('success-message');
    
    // 🔧 Google Form 設定從 hugo.toml 讀取
    form.action = '{{ site.Params.googleForms.actionURL | default "https://docs.google.com/forms/d/e/YOUR_FORM_ID_HERE/formResponse" }}';
    
    // 🔧 Email 欄位名稱從 hugo.toml 讀取
    // 實際的 entry.xxxxxxxx 值已設定在 HTML input 的 name 屬性中
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        const email = document.getElementById('email').value;
        
        // Basic email validation
        if (!email.includes('@') || email.length < 5) {
            e.preventDefault();
            alert('{{ i18n "contact.invalid_email" | default "請輸入有效的 Email 地址" }}');
            return;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
    });
    
    // 當 Google Forms 提交完成時調用
    window.submitEmailForm = function() {
        return true;
    };
    
    // 當隱藏的 iframe 載入完成時調用（表示提交成功）
    window.emailFormSubmitted = function() {
        // 隱藏表單，顯示成功訊息
        form.style.display = 'none';
        successMessage.classList.remove('d-none');
        
        // 滾動到成功訊息
        successMessage.scrollIntoView({ behavior: 'smooth' });
        
        // 可選：發送 Google Analytics 事件
        if (typeof gtag !== 'undefined') {
            gtag('event', 'email_submit', {
                'event_category': 'Contact',
                'event_label': 'Email Collection'
            });
        }
        
        // 重置按鈕狀態
        setTimeout(function() {
            submitBtn.disabled = false;
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
        }, 1000);
    };
});
</script>

{{partial "instafeed.html" . }}

{{ end }}