<!DOCTYPE html>
<html lang="{{ with site.LanguageCode }}{{ . }}{{ else }}en-US{{ end }}">
    <head>
        <meta charset="utf-8">
        <title>{{ with .Title }}{{ . }}{{ else }}{{ with site.Title }}{{ . }}{{ end }}{{ end }}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5">
        <meta name="description" content="{{ with .Description }}{{ . }}{{ else }}{{ with site.Params.description }}{{ . }}{{ end }}{{ end }}">
        {{ with site.Params.author }}<meta name="author" content="{{ . }}">{{ end }}
        
        <!-- 關鍵CSS內聯 -->
        <style>
        /* 最小關鍵CSS以加速首次渲染 */
        body{margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Helvetica,Arial,sans-serif;line-height:1.5}
        header{position:relative}
        .container{width:100%;max-width:1200px;margin:0 auto;padding:0 15px}
        img{max-width:100%;height:auto}
        .bg-secondary{background-color:#f8f9fa}
        </style>
        
        <!-- 預加載關鍵資源 -->
        <link rel="preload" href="{{ `fonts/themify-icons.woff` | absURL }}" as="font" type="font/woff" crossorigin>
        {{ if .IsHome }}
        <link rel="preload" href="{{ .Site.Params.banner.image | absURL }}" as="image" fetchpriority="high">
        {{ end }}
        
        {{ "<!-- plugins -->" | safeHTML }}
        {{ range site.Params.plugins.css }}
        <link rel="stylesheet" href="{{ .link | absURL }}">
        {{ end }}
        
        <!-- 關鍵主題樣式 -->
        {{ $style := resources.Get "css/style.css" | minify }}
        <link rel="stylesheet" href="{{ $style.Permalink }}" media="print" onload="this.media='all'">
        <noscript><link rel="stylesheet" href="{{ $style.Permalink }}"></noscript>
        
        {{ "<!-- 自定義樣式 -->" | safeHTML }}
        {{ $custom := resources.Get "css/custom.css" }}
        {{ with $custom }}
        {{ $custom := $custom | minify | fingerprint }}
        <link rel="stylesheet" href="{{ $custom.Permalink }}" media="print" onload="this.media='all'">
        <noscript><link rel="stylesheet" href="{{ $custom.Permalink }}"></noscript>
        {{ end }}
        
        <!-- DNS預取與預連接 -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        
        <!--Favicon-->
        <link rel="shortcut icon" href="{{ `images/favicon.png` | absURL }}" type="image/x-icon">
        <link rel="icon" href="{{ `images/favicon.png` | absURL }}" type="image/x-icon">
        
        {{ template "_internal/opengraph.html" . }}
    </head>
    <body>
        {{- partial "preloader.html" . -}}
        {{- partial "header.html" . -}}
        <main>
            {{- block "main" . }}{{- end }}
        </main>
        {{- partial "footer.html" . -}}
    </body>
</html>
