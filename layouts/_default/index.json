{{- $.Scratch.Add "index" slice -}}
{{- range site.RegularPages -}}
    {{- if and (not .Params.noSearch) (ne .Layout "search") -}}
        {{- $tags := "" -}}
        {{- $categories := "" -}}
        {{- with .Params.tags -}}{{- $tags = delimit . ", " -}}{{- end -}}
        {{- with .Params.categories -}}{{- $categories = delimit . ", " -}}{{- end -}}
        {{- $.Scratch.Add "index" (dict "title" .Title "content" .Plain "permalink" .Permalink "tags" $tags "categories" $categories) -}}
    {{- end -}}
{{- end -}}
{{- $.Scratch.Get "index" | jsonify -}}
