{{ define "main" }}

<section class="section">
  <div class="container">
    <div class="row">
      <div class="col-lg-12">
        <h2 class="mb-4">{{.Title }}</h2>
        {{ partial "image.html" (dict "Src" .Params.Image "Alt" "Image" "Class" "img-fluid w-100 mb-4" ) }}
        {{ .Content }}
        
        <!-- Google Forms 聯絡表單 -->
        <form id="google-contact-form" 
              class="row mt-5" 
              method="POST"
              target="hidden_iframe"
              onsubmit="submitGoogleForm()">
          
          <div class="col-lg-6">
            <label for="name" class="form-label">{{ i18n "contact.your_name" | default "Your Name" }} *</label>
            <input type="text" 
                   class="form-control mb-4" 
                   name="entry.NAME_FIELD_ID" 
                   id="name" 
                   placeholder="{{ i18n "contact.your_name" | default "Your Name" }}" 
                   required
                   minlength="2"
                   maxlength="100">
            <small class="form-text text-muted">{{ i18n "contact.name_help" | default "請輸入您的真實姓名" }}</small>
          </div>
          
          <div class="col-lg-6">
            <label for="email" class="form-label">{{ i18n "contact.your_email" | default "Your Email" }} *</label>
            <input type="email" 
                   class="form-control mb-4" 
                   name="entry.EMAIL_FIELD_ID" 
                   id="email" 
                   placeholder="{{ i18n "contact.your_email" | default "Your Email" }}" 
                   required
                   pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
            <small class="form-text text-muted">{{ i18n "contact.email_help" | default "我們會在24小時內回覆您" }}</small>
          </div>
          
          <div class="col-lg-6">
            <label for="company" class="form-label">{{ i18n "contact.company" | default "Company" }}</label>
            <input type="text" 
                   class="form-control mb-4" 
                   name="entry.COMPANY_FIELD_ID" 
                   id="company" 
                   placeholder="{{ i18n "contact.company" | default "Company (Optional)" }}"
                   maxlength="100">
          </div>
          
          <div class="col-lg-6">
            <label for="phone" class="form-label">{{ i18n "contact.phone" | default "Phone" }}</label>
            <input type="tel" 
                   class="form-control mb-4" 
                   name="entry.PHONE_FIELD_ID" 
                   id="phone" 
                   placeholder="{{ i18n "contact.phone" | default "Phone (Optional)" }}"
                   pattern="[0-9+\-\s\(\)]{8,20}">
          </div>
          
          <div class="col-12">
            <label for="subject" class="form-label">{{ i18n "contact.subject" | default "Subject" }} *</label>
            <select class="form-control mb-4" name="entry.SUBJECT_FIELD_ID" id="subject" required>
              <option value="">{{ i18n "contact.select_subject" | default "Select a subject..." }}</option>
              <option value="Business Strategy Consultation">{{ i18n "contact.business_strategy" | default "Business Strategy Consultation" }}</option>
              <option value="Digital Marketing Services">{{ i18n "contact.digital_marketing" | default "Digital Marketing Services" }}</option>
              <option value="Website Development">{{ i18n "contact.website_development" | default "Website Development" }}</option>
              <option value="Partnership Opportunities">{{ i18n "contact.partnership" | default "Partnership Opportunities" }}</option>
              <option value="Career Inquiries">{{ i18n "contact.career" | default "Career Inquiries" }}</option>
              <option value="Other">{{ i18n "contact.other" | default "Other" }}</option>
            </select>
          </div>
          
          <div class="col-12">
            <label for="message" class="form-label">{{ i18n "contact.message" | default "Message" }} *</label>
            <textarea name="entry.MESSAGE_FIELD_ID" 
                      id="message" 
                      class="form-control mb-4" 
                      placeholder="{{ i18n "contact.message_placeholder" | default "Please describe your inquiry in detail..." }}" 
                      rows="6" 
                      required
                      minlength="10"
                      maxlength="2000"></textarea>
            <small class="form-text text-muted">
              <span id="char-count">0</span>/2000 {{ i18n "contact.characters" | default "characters" }}
            </small>
          </div>
          
          <!-- 隱私政策同意 -->
          <div class="col-12 mb-4">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="entry.PRIVACY_FIELD_ID" id="privacy-consent" required value="同意隱私政策">
              <label class="form-check-label" for="privacy-consent">
                {{ i18n "contact.privacy_consent" | default "I agree to the privacy policy and terms of service" }} *
              </label>
            </div>
          </div>
          
          <div class="col-12">
            <button type="submit" class="btn btn-primary btn-lg" id="submit-btn">
              <span class="btn-text">{{ i18n "contact.send_message" | default "Send Message" }}</span>
              <span class="btn-loading d-none">
                <i class="fas fa-spinner fa-spin mr-2"></i>{{ i18n "contact.sending" | default "Sending..." }}
              </span>
            </button>
          </div>
        </form>
        
        <!-- 隱藏的 iframe 用於 Google Forms 提交 -->
        <iframe name="hidden_iframe" id="hidden_iframe" style="display:none;" onload="formSubmitted()"></iframe>
        
        <!-- 成功訊息 -->
        <div id="success-message" class="alert alert-success mt-4 d-none">
          <h5><i class="fas fa-check-circle mr-2"></i>{{ i18n "contact.success_title" | default "Message Sent Successfully!" }}</h5>
          <p class="mb-0">{{ i18n "contact.success_message" | default "Thank you for contacting us. We'll get back to you within 24 hours." }}</p>
        </div>
        
        <!-- 聯絡資訊 -->
        <div class="row mt-5">
          <div class="col-lg-4 mb-4">
            <div class="contact-info">
              <h5><i class="ti-email mr-2"></i>{{ i18n "common.email" | default "Email" }}</h5>
              <p><a href="mailto:{{ site.Params.email }}">{{ site.Params.email }}</a></p>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="contact-info">
              <h5><i class="ti-mobile mr-2"></i>{{ i18n "common.phone" | default "Phone" }}</h5>
              <p><a href="tel:{{ site.Params.mobile }}">{{ site.Params.mobile }}</a></p>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="contact-info">
              <h5><i class="ti-location-pin mr-2"></i>{{ i18n "common.address" | default "Address" }}</h5>
              <p>{{ site.Params.address }}</p>
            </div>
          </div>
        </div>
        
        <!-- 回應時間說明 -->
        <div class="alert alert-info mt-4">
          <h6><i class="ti-info mr-2"></i>{{ i18n "contact.response_time_title" | default "Response Time" }}</h6>
          <p class="mb-0">{{ i18n "contact.response_time_text" | default "We typically respond to all inquiries within 24 hours during business days. For urgent matters, please call us directly." }}</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Google Forms JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('google-contact-form');
    const submitBtn = document.getElementById('submit-btn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    const messageTextarea = document.getElementById('message');
    const charCount = document.getElementById('char-count');
    const successMessage = document.getElementById('success-message');
    
    // 設定 Google Form 的 action URL
    // 請將此 URL 替換為您的 Google Form 的 formResponse URL
    form.action = 'https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse';
    
    // Character counter
    messageTextarea.addEventListener('input', function() {
        const count = this.value.length;
        charCount.textContent = count;
        
        if (count > 1900) {
            charCount.style.color = '#dc3545';
        } else if (count > 1500) {
            charCount.style.color = '#ffc107';
        } else {
            charCount.style.color = '#6c757d';
        }
    });
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.disabled = true;
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        
        // Basic client-side validation
        const email = document.getElementById('email').value;
        const message = document.getElementById('message').value;
        const name = document.getElementById('name').value;
        const subject = document.getElementById('subject').value;
        const privacyConsent = document.getElementById('privacy-consent').checked;
        
        if (!email.includes('@') || message.length < 10 || !name || !subject || !privacyConsent) {
            e.preventDefault();
            alert('請填寫所有必填欄位並同意隱私政策。');
            resetSubmitButton();
            return;
        }
        
        // 如果驗證通過，表單會正常提交到 Google Forms
    });
    
    function resetSubmitButton() {
        submitBtn.disabled = false;
        btnText.classList.remove('d-none');
        btnLoading.classList.add('d-none');
    }
    
    // 當 Google Forms 提交完成時調用
    window.submitGoogleForm = function() {
        // 表單提交邏輯已在 form 的 onsubmit 中處理
        return true;
    };
    
    // 當隱藏的 iframe 載入完成時調用（表示提交成功）
    window.formSubmitted = function() {
        // 隱藏表單，顯示成功訊息
        form.style.display = 'none';
        successMessage.classList.remove('d-none');
        
        // 滾動到成功訊息
        successMessage.scrollIntoView({ behavior: 'smooth' });
        
        // 可選：發送 Google Analytics 事件
        if (typeof gtag !== 'undefined') {
            gtag('event', 'form_submit', {
                'event_category': 'Contact',
                'event_label': 'Contact Form Submission'
            });
        }
        
        // 重置按鈕狀態
        resetSubmitButton();
    };
});
</script>

{{partial "instafeed.html" . }}

{{ end }}