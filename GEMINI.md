# Workspace AI Rules for Hugo Static Site Generator Project using Parsa-Hugo Theme

**General Guidelines:**

* This project uses <PERSON>, a static site generator, with the parsa-hugo theme.
* **Do not modify any files within the `themes/parsa-hugo/` directory.** All customizations must be implemented through <PERSON>’s supported configuration mechanisms (e.g., custom layouts, `config.toml`, `assets`, `data`, or `static` folders), as recommended by the official Hugo and parsa-hugo theme documentation.
* Before making any configuration changes or adjustments, always use the Context 7 MCP tool to search for information related to **‘Hugo static site generator’** settings or modifications.
* If any changes involve the parsa-hugo theme, first use the Context 7 MCP tool to search for relevant information about **‘parsa-hugo theme’** settings or modifications before proceeding.

**Language and Response Style:**

* Please respond in clear and professional English.
* Maintain a concise and helpful tone.

**Project Context:**

* The site is built with <PERSON> and styled with the parsa-hugo theme.
* Prioritize site performance, SEO best practices, and maintainability in all code and configuration suggestions.

**Coding and Configuration Practices:**

* Follow official Hugo documentation and parsa-hugo theme guidelines.
* Validate all configuration changes through Context 7 MCP searches before implementation.
* **Never make unsupported or undocumented changes to the theme code itself.**
* When suggesting code or configuration, ensure compatibility with the latest stable Hugo release.
* **All changes should be made in the `custom` folder structure (e.g., `/layouts/_default`, `/layouts/partials`, `/assets`) as per Hugo's override mechanism.**

**Error Handling and Testing:**

* Suggest robust error handling where applicable.
* Encourage testing of configuration changes locally before deployment.

**Collaboration and Communication:**

* If uncertain about a setting or modification, recommend consulting official resources or team members.
* Document any changes clearly for future reference.
