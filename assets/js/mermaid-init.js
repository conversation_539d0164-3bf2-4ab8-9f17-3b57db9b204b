document.addEventListener('DOMContentLoaded', function() {
  // 直接初始化 mermaid
  if (typeof mermaid !== 'undefined') {
    mermaid.initialize({
      startOnLoad: true,  // 讓 mermaid 自動處理圖表
      theme: 'default',
      flowchart: {
        useMaxWidth: false,
        htmlLabels: true
      },
      securityLevel: 'loose'
    });
  }

  // 尋找所有前綴為「mermaid」的預格式化文字區塊
  const blocks = document.querySelectorAll('pre');
  blocks.forEach(function(block) {
    const content = block.textContent.trim();
    if (content.startsWith('graph ') || 
        content.startsWith('flowchart ') || 
        content.startsWith('sequenceDiagram') || 
        content.startsWith('classDiagram') || 
        content.startsWith('gantt') || 
        content.startsWith('pie') || 
        content.startsWith('erDiagram')) {
      
      // 建立 mermaid 圖表容器
      const mermaidDiv = document.createElement('div');
      mermaidDiv.className = 'mermaid';
      mermaidDiv.textContent = content;
      
      // 將圖表容器插入到文本區塊之前，並隱藏原始代碼區塊
      block.parentNode.insertBefore(mermaidDiv, block);
      block.style.display = 'none';
      
      // 如果 mermaid 已加載，嘗試重新渲染
      if (typeof mermaid !== 'undefined') {
        try {
          mermaid.init(undefined, mermaidDiv);
        } catch (e) {
          console.error('Mermaid initialization error:', e);
          // 如果渲染失敗，顯示原始代碼區塊
          block.style.display = 'block';
          mermaidDiv.style.display = 'none';
        }
      }
    }
  });
});
