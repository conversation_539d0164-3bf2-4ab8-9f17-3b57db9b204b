/**
 * code-copy.js - 為程式碼區塊添加複製按鈕功能
 * 基於 Hugo 官方最佳實踐實現的程式碼複製功能
 * 支援語法高亮顯示與語言標籤
 */
document.addEventListener('DOMContentLoaded', function() {
  // 獲取所有程式碼區塊（Hugo 語法高亮和模板定義）
  const codeBlocks = document.querySelectorAll('pre');
  
  // 為每個程式碼區塊添加複製按鈕
  codeBlocks.forEach(function(codeBlock) {
    // 設置程式碼區塊的相對定位，用於定位複製按鈕
    if (!codeBlock.style.position || codeBlock.style.position === 'static') {
      codeBlock.style.position = 'relative';
    }
    
    // 檢測是否為 Hugo 的 Chroma 語法高亮區塊
    const isChromaHighlighted = codeBlock.classList.contains('highlight') || 
                               codeBlock.classList.contains('chroma') || 
                               codeBlock.querySelector('.chroma');
    
    // 創建複製按鈕
    const copyButton = document.createElement('button');
    copyButton.className = 'copy-button';
    copyButton.type = 'button';
    copyButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/><path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/></svg>';
    copyButton.setAttribute('aria-label', '複製程式碼');
    copyButton.setAttribute('title', '複製程式碼');
    
    // 添加複製功能
    copyButton.addEventListener('click', function() {
      // 找到程式碼元素，考慮 Hugo 的 Chroma 語法高亮區塊的特殊結構
      let textToCopy = '';
      
      if (isChromaHighlighted) {
        // 處理 Hugo 的 Chroma 語法高亮區塊
        const codeElement = codeBlock.querySelector('td.code') || codeBlock.querySelector('code');
        if (codeElement) {
          textToCopy = codeElement.textContent || codeElement.innerText;
        } else {
          // 如果沒有找到特定結構，則使用整個 pre 元素的內容
          textToCopy = codeBlock.textContent || codeBlock.innerText;
        }
      } else {
        // 一般的程式碼區塊
        const codeElement = codeBlock.querySelector('code');
        textToCopy = codeElement ? (codeElement.textContent || codeElement.innerText) : (codeBlock.textContent || codeBlock.innerText);
      }
      
      // 清理可能的額外空格和空行
      textToCopy = textToCopy.trim();
      
      // 嘗試使用現代 Clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(textToCopy)
          .then(function() {
            showCopiedFeedback();
          })
          .catch(function() {
            // 如果現代 API 失敗，回退到傳統方法
            fallbackCopyToClipboard(textToCopy);
          });
      } else {
        // 如果瀏覽器不支援現代 API，使用傳統方法
        fallbackCopyToClipboard(textToCopy);
      }
      
      // 傳統複製方法
      function fallbackCopyToClipboard(text) {
        try {
          const textarea = document.createElement('textarea');
          textarea.value = text;
          textarea.setAttribute('readonly', '');
          textarea.style.position = 'fixed';
          textarea.style.top = '0';
          textarea.style.left = '-9999px';
          document.body.appendChild(textarea);
          
          const range = document.createRange();
          range.selectNodeContents(textarea);
          
          const selection = window.getSelection();
          selection.removeAllRanges();
          selection.addRange(range);
          
          textarea.select();
          const successful = document.execCommand('copy');
          
          document.body.removeChild(textarea);
          
          if (successful) {
            showCopiedFeedback();
          } else {
            console.error('無法複製文本');
          }
        } catch (err) {
          console.error('複製時發生錯誤:', err);
        }
      }
      
      // 顯示複製成功的視覺反饋
      function showCopiedFeedback() {
        copyButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/></svg>';
        copyButton.classList.add('copied');
        
        setTimeout(function() {
          copyButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/><path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/></svg>';
          copyButton.classList.remove('copied');
        }, 2000);
      }
    });
    
    // 將按鈕添加到程式碼區塊
    codeBlock.appendChild(copyButton);
    
    // 添加程式碼語言標籤
    const codeElement = codeBlock.querySelector('code');
    if (codeElement) {
      // 嘗試從 class 名稱中提取語言
      let language = '';
      const classNames = codeElement.className.split(' ');
      
      for (let i = 0; i < classNames.length; i++) {
        const className = classNames[i];
        
        // 尋找符合 Hugo 的語言標記模式
        if (className.startsWith('language-')) {
          language = className.replace('language-', '');
          break;
        } else if (className.startsWith('lang-')) {
          language = className.replace('lang-', '');
          break;
        }
      }
      
      // 如果找到語言，則設置 data-lang 屬性
      if (language) {
        codeBlock.setAttribute('data-lang', language);
      } else {
        // 檢查是否為 Hugo 的 Chroma 高亮區塊
        const chromaElement = codeBlock.querySelector('.chroma') || codeBlock;
        if (chromaElement && chromaElement.classList) {
          for (let i = 0; i < chromaElement.classList.length; i++) {
            const className = chromaElement.classList[i];
            // 尋找形如 language-go, language-js, language-css 等的類名
            if (className.startsWith('language-') || className.startsWith('lang-')) {
              language = className.replace(/^(language-|lang-)/, '');
              codeBlock.setAttribute('data-lang', language);
              break;
            }
          }
        }
      }
    }
  });
});
