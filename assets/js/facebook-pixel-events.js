/**
 * 增強型 Facebook Pixel 事件追蹤
 * 提供更詳細的用戶行為分析，支援多種內容互動事件
 */
document.addEventListener('DOMContentLoaded', function() {
  // 確保 fbq 函數存在
  if (typeof fbq !== 'function') return;

  // 1. 內容閱讀追蹤 - 針對文章頁面
  if (document.querySelector('article')) {
    // 獲取文章標題和分類
    const articleTitle = document.title;
    const categories = [];
    const categoryElements = document.querySelectorAll('.post-category, .category-link');
    categoryElements.forEach(el => categories.push(el.textContent.trim()));
    
    // 追蹤文章閱讀事件
    fbq('track', 'ViewContent', {
      content_type: 'article',
      content_name: articleTitle,
      content_category: categories.join(',')
    });
    
    // 追蹤閱讀深度 (透過滾動事件)
    let maxScrollPercentage = 0;
    window.addEventListener('scroll', function() {
      const scrollTop = window.scrollY;
      const docHeight = document.querySelector('article').offsetHeight;
      const windowHeight = window.innerHeight;
      const scrollPercent = (scrollTop / (docHeight - windowHeight)) * 100;
      const roundedPercent = Math.floor(scrollPercent);
      
      // 只在達到 25%, 50%, 75%, 100% 時觸發事件
      if (roundedPercent >= 25 && maxScrollPercentage < 25) {
        maxScrollPercentage = 25;
        fbq('trackCustom', 'ReadDepth', { percentage: 25, article: articleTitle });
      } else if (roundedPercent >= 50 && maxScrollPercentage < 50) {
        maxScrollPercentage = 50;
        fbq('trackCustom', 'ReadDepth', { percentage: 50, article: articleTitle });
      } else if (roundedPercent >= 75 && maxScrollPercentage < 75) {
        maxScrollPercentage = 75;
        fbq('trackCustom', 'ReadDepth', { percentage: 75, article: articleTitle });
      } else if (roundedPercent >= 90 && maxScrollPercentage < 90) {
        maxScrollPercentage = 100;
        fbq('trackCustom', 'ReadDepth', { percentage: 100, article: articleTitle });
        fbq('trackCustom', 'CompleteArticleRead', { article: articleTitle });
      }
    }, { passive: true });
  }

  // 2. 搜索行為追蹤
  const searchForms = document.querySelectorAll('form[role="search"]');
  if (searchForms.length > 0) {
    searchForms.forEach(form => {
      form.addEventListener('submit', function(e) {
        const searchInput = form.querySelector('input[type="search"]');
        if (searchInput && searchInput.value) {
          fbq('track', 'Search', {
            search_string: searchInput.value
          });
        }
      });
    });
  }

  // 3. 外部連結點擊追蹤
  document.querySelectorAll('a[href^="http"]:not([href*="' + window.location.hostname + '"])').forEach(link => {
    link.addEventListener('click', function(e) {
      fbq('trackCustom', 'ExternalLinkClick', {
        destination: this.href,
        link_text: this.textContent.trim() || 'Image Link'
      });
    });
  });

  // 4. 標籤點擊事件
  document.querySelectorAll('.post-tag, .tag-link, a[href^="/tags/"]').forEach(tag => {
    tag.addEventListener('click', function(e) {
      fbq('trackCustom', 'TagClick', {
        tag_name: this.textContent.trim()
      });
    });
  });

  // 5. 文章互動事件 (如果有評論系統)
  if (document.querySelector('#comments, .comments, .utterances, .disqus_thread')) {
    // 監測用戶是否滾動到評論區
    const commentObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          fbq('trackCustom', 'ViewComments', {
            article: document.title
          });
          commentObserver.disconnect(); // 只觸發一次
        }
      });
    });
    
    const commentSection = document.querySelector('#comments, .comments, .utterances, .disqus_thread');
    if (commentSection) {
      commentObserver.observe(commentSection);
    }
  }
});

// 6. 網站停留時間追蹤
window.addEventListener('load', function() {
  const startTime = new Date().getTime();
  
  window.addEventListener('beforeunload', function() {
    const endTime = new Date().getTime();
    const timeSpent = Math.floor((endTime - startTime) / 1000); // 轉換為秒
    
    // 只在停留超過10秒時記錄，避免跳轉頁面的干擾
    if (timeSpent > 10) {
      // 使用 navigator.sendBeacon 確保數據在頁面卸載時能成功發送
      if (typeof fbq === 'function') {
        fbq('trackCustom', 'TimeOnPage', {
          seconds: timeSpent,
          page: window.location.pathname
        });
      }
    }
  });
});

// 7. 優化 - 確保在未來導航(SPA)時也能正確跟踪頁面瀏覽
// 如果網站使用了 PJAX 或其他前端路由
if (typeof fbq === 'function') {
  const originalPushState = history.pushState;
  history.pushState = function() {
    originalPushState.apply(this, arguments);
    
    // 延遲執行以確保 DOM 已更新
    setTimeout(() => {
      fbq('track', 'PageView');
    }, 300);
  };
}
