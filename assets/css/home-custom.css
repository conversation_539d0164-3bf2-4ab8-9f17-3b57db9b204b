/* 首頁自定義樣式 - 專業UI/UX優化 */

/* 導航欄樣式調整 */
.navbar-brand img {
  max-height: 72px;
  width: auto;
}

/* 全域變數 */
:root {
  --primary-color: #ff6f61;
  --primary-dark: #e5625b;
  --primary-light: #ff8c80;
  --text-dark: #333333;
  --text-medium: #666666;
  --text-light: #999999;
  --bg-light: #f9f9f9;
  --transition: all 0.3s ease;
}

/* 全域動畫 */
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 111, 97, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(255, 111, 97, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 111, 97, 0); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* 通用區塊樣式 */
.section {
  padding: 80px 0;
}

.section-heading {
  margin-bottom: 40px;
}

.section-tag {
  display: inline-block;
  padding: 5px 15px;
  background-color: rgba(255, 111, 97, 0.1);
  color: var(--primary-color);
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 15px;
  position: relative;
}

.section-description {
  color: var(--text-medium);
  font-size: 1.1rem;
  max-width: 700px;
  margin: 0 auto;
}

/* 英雄區塊樣式 */
.hero-section {
  padding: 120px 0 100px;
  background-color: #ffffff;
  position: relative;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.6) 100%);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-tag {
  display: inline-block;
  background-color: rgba(255, 111, 97, 0.1);
  color: var(--primary-color);
  padding: 8px 20px;
  border-radius: 30px;
  font-weight: 600;
  margin-bottom: 20px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--text-dark);
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-description {
  font-size: 1.2rem;
  color: var(--text-medium);
  margin-bottom: 30px;
  max-width: 600px;
}

.hero-buttons {
  margin-bottom: 30px;
}

.hero-buttons .btn {
  padding: 12px 28px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 5px;
  transition: var(--transition);
}

.hero-buttons .btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.hero-buttons .btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
}

.hero-buttons .btn-outline-light {
  color: var(--text-dark);
  border-color: #ddd;
  background-color: white;
}

.hero-buttons .btn-outline-light:hover {
  background-color: #f8f8f8;
  border-color: #ccc;
  transform: translateY(-2px);
}

.pulse-btn {
  animation: pulse 2s infinite;
}

.hero-social {
  display: flex;
  align-items: center;
}

.social-label {
  margin-right: 15px;
  color: var(--text-medium);
  font-weight: 500;
}

.social-icons {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
}

.social-icons li {
  margin-right: 15px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: var(--text-medium);
  transition: var(--transition);
}

.social-icon:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-3px);
}

.hero-image-wrapper {
  position: relative;
  z-index: 2;
}

.hero-image {
  width: 100%;
  max-width: 450px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  animation: float 6s ease-in-out infinite;
}

.hero-shape-1 {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255,111,97,0.2) 0%, rgba(255,111,97,0.05) 100%);
  top: -50px;
  left: -50px;
  z-index: -1;
}

.hero-shape-2 {
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(79,172,254,0.2) 0%, rgba(79,172,254,0.05) 100%);
  bottom: -30px;
  right: 30px;
  z-index: -1;
}

/* 主題探索區塊樣式 */
.topic-section {
  background-color: var(--bg-light);
}

.topic-card {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  height: 100%;
  box-shadow: 0 10px 30px rgba(0,0,0,0.05);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.topic-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.topic-icon-wrapper {
  margin-bottom: 25px;
}

.topic-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin-bottom: 20px;
  transition: var(--transition);
}

.ai-card .topic-icon {
  background-color: rgba(255, 111, 97, 0.1);
  color: var(--primary-color);
}

.code-card .topic-icon {
  background-color: rgba(79, 172, 254, 0.1);
  color: #4FACFE;
}

.system-card .topic-icon {
  background-color: rgba(108, 91, 231, 0.1);
  color: #6C5BE7;
}

.topic-card:hover .topic-icon {
  transform: rotate(10deg) scale(1.1);
}

.topic-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.topic-content p {
  color: var(--text-medium);
  margin-bottom: 25px;
  line-height: 1.6;
}

.topic-link {
  display: flex;
  align-items: center;
  color: var(--primary-color);
  font-weight: 600;
  transition: var(--transition);
}

.topic-link i {
  margin-left: 8px;
  transition: var(--transition);
}

.topic-link:hover {
  color: var(--primary-dark);
}

.topic-link:hover i {
  transform: translateX(5px);
}

/* 最新文章區塊樣式 */
.latest-articles {
  background-color: white;
}

.article-card {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: var(--transition);
}

.article-card:hover {
  transform: translateY(-7px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.article-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.article-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.7s ease;
}

.image-link:hover .article-img {
  transform: scale(1.05);
}

.article-category {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 111, 97, 0.9);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  transition: var(--transition);
  z-index: 10;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.article-category:hover {
  background-color: var(--primary-dark);
  color: white;
}

.article-category-badge {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 15px;
  transition: var(--transition);
}

.article-body {
  padding: 25px 20px;
}

.article-meta {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.article-date {
  font-size: 0.85rem;
  color: var(--text-light);
  margin-right: 15px;
}

.article-date i,
.article-tags i {
  margin-right: 5px;
}

.article-tags {
  font-size: 0.85rem;
}

.article-tags a {
  color: var(--primary-color);
  margin-right: 5px;
  transition: var(--transition);
}

.article-tags a:hover {
  color: var(--primary-dark);
}

.article-title {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.4;
  margin-bottom: 15px;
}

.article-title a {
  color: var(--text-dark);
  transition: var(--transition);
}

.article-title a:hover {
  color: var(--primary-color);
}

.article-excerpt {
  color: var(--text-medium);
  margin-bottom: 25px;
  font-size: 0.95rem;
  line-height: 1.6;
}

.article-link {
  display: flex;
  align-items: center;
  color: var(--primary-color);
  font-weight: 600;
  transition: var(--transition);
}

.article-link i {
  margin-left: 8px;
  transition: var(--transition);
}

.article-link:hover {
  color: var(--primary-dark);
}

.article-link:hover i {
  transform: translateX(5px);
}

/* 標籤雲區塊樣式 */
.tag-section {
  background-color: var(--bg-light);
}

.tags-container {
  background-color: white;
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.05);
}

.tag-categories {
  margin-bottom: 30px;
}

.tag-category-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.tag-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  padding: 8px 20px;
  background-color: #f5f5f5;
  color: var(--text-dark);
  border-radius: 30px;
  text-decoration: none;
  transition: var(--transition);
  font-weight: 500;
}

.tag-name {
  margin-right: 5px;
}

.tag-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  padding: 0 8px;
  background-color: white;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-medium);
}

.tag-item:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-3px);
}

.tag-item:hover .tag-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 訂閱區塊樣式 */
.newsletter-section {
  background-color: white;
  position: relative;
  overflow: hidden;
}

.newsletter-form {
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-form .input-group {
  box-shadow: 0 10px 30px rgba(0,0,0,0.05);
  border-radius: 5px;
  overflow: hidden;
}

.newsletter-form .form-control {
  height: 60px;
  border: none;
  padding: 0 20px;
  font-size: 1rem;
}

.newsletter-form .form-control:focus {
  box-shadow: none;
}

.newsletter-form .btn {
  padding: 0 30px;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  font-weight: 600;
  height: 60px;
  transition: var(--transition);
}

.newsletter-form .btn:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

/* 響應式調整 */
@media (max-width: 991px) {
  .hero-section {
    padding: 80px 0;
  }
  
  .hero-title {
    font-size: 2.8rem;
  }
  
  .hero-image-wrapper {
    margin-top: 40px;
  }
  
  .section {
    padding: 60px 0;
  }
  
  .section-title {
    font-size: 2rem;
  }
}

@media (max-width: 767px) {
  .hero-section {
    padding: 60px 0;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2.2rem;
  }
  
  .hero-description {
    max-width: 100%;
  }
  
  .hero-social {
    justify-content: center;
  }
  
  .topic-card {
    margin-bottom: 30px;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
  
  .tags-container {
    padding: 25px;
  }
}
