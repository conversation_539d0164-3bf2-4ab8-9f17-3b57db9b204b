/* 
 * 自定義 CSS 樣式 - Parsa 主題優化
 * 這個文件通過 Hugo 的資產管道 (<PERSON>) 進行處理
 * 遵循 Hugo 主題自定義的最佳實踐
 */

/* 頁尾樣式調整 */
footer {
  max-width: 100%;
  margin: 0 auto;
  padding: 2rem 0 0;
  /* 減少頁腳的高度比例 */
}

footer .section {
  padding: 2rem 0;
}

/* 頁尾 Logo 大小限制 */
footer .img-fluid {
  max-height: 72px;
  width: auto;
}

/* 社交媒體圖標大小調整 */
footer .list-inline-item a {
  font-size: 28px;
  margin: 0 10px;
  display: inline-block;
}

footer .list-inline-item:first-child a {
  margin-left: 0;
}

footer .list-inline-item i {
  vertical-align: middle;
  line-height: 72px;
}

/* 文章預覽圖像比例控制 - 強制控制 */
.article-card .article-image,
.post-card .post-image,
div[class*="article-image"],
div[class*="post-image"] {
  position: relative !important;
  overflow: hidden !important;
  height: 200px !important;
  padding-top: 0 !important;
  padding-right: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0 !important;
  max-height: none !important;
  min-height: 0 !important;
}

.article-card .article-image a,
.post-card .post-image a,
div[class*="article-image"] a,
div[class*="post-image"] a {
  display: block !important;
  width: 100% !important;
  height: 100% !important;
}

.article-card .article-image img,
.post-card .post-image img,
div[class*="article-image"] img,
div[class*="post-image"] img,
.article-img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  object-position: center !important;
  transition: transform 0.3s ease !important;
  max-height: none !important;
  min-height: 0 !important;
}

/* 圖片懸停效果 */
.article-card:hover img,
.post-card:hover img,
div[class*="article-image"]:hover img,
div[class*="post-image"]:hover img {
  transform: scale(1.05) !important;
}

/* 1. 改善中文閱讀體驗 */
body {
  font-family: 'Noto Sans TC', 'Open Sans', sans-serif;
  line-height: 1.7;
  color: #333;
  font-size: 16px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
}

/* 2. 提升內容可讀性 */
.content {
  max-width: 90ch;
  margin: 0 auto;
}

.content p {
  margin-bottom: 1.5rem;
  letter-spacing: 0.01rem;
}

/* 3. 優化標題層級 */
h1, h2, h3, h4, h5, h6 {
  color: #222;
  font-weight: 700;
  letter-spacing: 0.01rem;
}

h2 {
  font-size: 1.8rem;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

h3 {
  font-size: 1.5rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

/* 4. 優化程式碼區塊 */
pre {
  background-color: #f8f9fa !important;
  border-radius: 8px;
  padding: 1.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
  border: 1px solid #e9ecef;
  position: relative; /* 為複製按鈕定位做準備 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

code {
  font-family: 'Source Code Pro', Consolas, Monaco, monospace;
  font-size: 0.9rem;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  background-color: #f0f0f0;
  color: #333;
  line-height: 1.6;
}

pre code {
  background-color: transparent;
  padding: 0;
  font-size: 0.95rem;
  color: #333;
  line-height: 1.7;
  tab-size: 4;
}

/* 程式碼複製按鈕樣式 */
.copy-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem 0.5rem;
  border: none;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
  transition: all 0.2s ease-in-out;
  opacity: 0;
  z-index: 10;
}

pre:hover .copy-button {
  opacity: 1;
}

.copy-button:hover {
  background-color: rgba(255, 255, 255, 0.9);
  color: #000;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.copy-button.copied {
  background-color: #b3e6b3;
  color: #006600;
}

/* 程式碼語言標籤 */
pre::before {
  content: attr(data-lang);
  position: absolute;
  top: 0;
  left: 0;
  padding: 0.25rem 0.7rem;
  font-size: 0.75rem;
  color: #666;
  background-color: #f0f0f0;
  border-radius: 0 0 4px 0;
  font-family: 'Source Code Pro', monospace;
  z-index: 5;
}

/* 5. 美化引用區塊 */
blockquote {
  border-left: 4px solid #6c757d;
  padding: 0.5rem 1rem;
  margin: 1.5rem 0;
  color: #495057;
  font-style: italic;
  background-color: #f8f9fa;
  border-radius: 0 4px 4px 0;
}

blockquote p {
  margin-bottom: 0.5rem !important;
}

/* 6. 優化列表顯示 */
ul, ol {
  padding-left: 2rem;
  margin-bottom: 1.5rem;
}

li {
  margin-bottom: 0.5rem;
  line-height: 1.7;
}

ul li {
  position: relative;
}

ul li::marker {
  color: #0066cc;
}

ol li::marker {
  color: #0066cc;
  font-weight: 500;
}

/* 左側垂直線樣式的列表 */
ul.styled-list {
  list-style: none;
  padding-left: 1.5rem;
}

ul.styled-list li {
  position: relative;
  padding-left: 1.2rem;
  margin-bottom: 0.8rem;
}

ul.styled-list li:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0066cc;
}

/* 緊湊列表樣式 */
ul.compact, ol.compact {
  margin-bottom: 1rem;
}

ul.compact li, ol.compact li {
  margin-bottom: 0.3rem;
}

/* 7. 優化表格樣式 */
table {
  width: 100%;
  margin: 1.5rem 0;
  border-collapse: collapse;
  font-size: 0.9rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

thead {
  border-bottom: 2px solid #dee2e6;
}

th, td {
  padding: 0.75rem 1rem;
  border: 1px solid #dee2e6;
  text-align: left;
  vertical-align: top;
}

th {
  background-color: #f0f4f8;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
}

tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

tbody tr:hover {
  background-color: #f0f4f8;
}

/* 8. 優化連結樣式 */
a {
  color: #0066cc;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s;
}

a:hover {
  color: #004999;
  border-bottom-color: currentColor;
}

/* 9. 改善文章卡片 */
.card {
  transition: transform 0.3s, box-shadow 0.3s;
  border-radius: 8px;
  overflow: hidden;
  border: none;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

/* 10. 導航欄優化 */
.navigation {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

/* 11. 頁腳優化 */
.footer {
  background-color: #f8f9fa;
  padding: 3rem 0;
}

/* 12. 改善 Buy Me A Coffee 按鈕 */
.buymeacoffee-container {
  margin: 2rem 0;
  text-align: center;
}

/* 13. 優化圖片顯示 */
.content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 1.5rem auto;
  display: block;
  box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

/* 14. 提升移動裝置體驗 */
@media (max-width: 768px) {
  body {
    font-size: 15px;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  h3 {
    font-size: 1.3rem;
  }
}

/* 15. 自定義滾動條 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* 16. Reduce heading font sizes sitewide */
h1, .h1 {
  font-size: 1.8rem !important;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1.post-title {
  font-size: 1.6rem !important;
}

/* Article titles on homepage and listing pages */
.article-content h2 a,
.post-content h2 a {
  font-size: 1.4rem !important;
  line-height: 1.2;
}

/* Adjust spacing around headings */
.post h1, .post h2, .post h3,
article h1, article h2, article h3 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

/* 17. Reduce tag cloud font size on homepage */
.tag-category-title {
  font-size: 1rem;
  margin-bottom: 0.8rem;
}

.tag-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-item {
  font-size: 0.8rem;
  padding: 0.3rem 0.6rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.2s;
  text-decoration: none;
  border: 1px solid #e9ecef;
}

.tag-item:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
}

.tag-name {
  font-size: 0.8rem;
  font-weight: 500;
  color: #495057;
}

.tag-count {
  font-size: 0.7rem;
  color: #868e96;
  margin-left: 0.3rem;
}

::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 16. Mermaid 圖表樣式 */
.mermaid {
  margin: 2rem auto;
  text-align: center;
  background-color: #fafafa;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow-x: auto;
  max-width: 100%;
}

/* 讓 Mermaid 圖表在手機上也能完整顯示 */
@media (max-width: 768px) {
  .mermaid {
    font-size: 12px;
    padding: 1rem;
  }
}

/* 優化不同圖表類型的樣式 */
.mermaid .node rect, 
.mermaid .node circle, 
.mermaid .node ellipse, 
.mermaid .node polygon,
.mermaid .node path {
  fill: #e1f5fe;
  stroke: #4fc3f7;
  stroke-width: 1px;
}

.mermaid .node.current rect, 
.mermaid .node.current circle, 
.mermaid .node.current ellipse, 
.mermaid .node.current polygon {
  fill: #bbdefb;
  stroke: #2196f3;
  stroke-width: 2px;
}

.mermaid .edgeLabel {
  background-color: #fff;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 0.85em;
}

.mermaid .cluster rect {
  fill: #f5f5f5;
  stroke: #bdbdbd;
  stroke-width: 1px;
  rx: 5px;
  ry: 5px;
}

/* 流程圖中的決策點菜菜對得更鮮明 */
.mermaid .node.rhombus rect,
.mermaid .node.rhombus circle, 
.mermaid .node.rhombus ellipse, 
.mermaid .node.rhombus polygon {
  fill: #fff8e1;
  stroke: #ffc107;
}

/* 終端節點的樣式 */
.mermaid .node.end rect,
.mermaid .node.end circle, 
.mermaid .node.end ellipse, 
.mermaid .node.end polygon {
  fill: #e8f5e9;
  stroke: #66bb6a;
}
