/* 目錄（TOC）樣式優化 */

/* 主容器樣式 - 高度限制和滾動 */
.td-toc, .toc, .TableOfContents, #TableOfContents {
  max-height: 80vh !important;
  overflow-y: auto !important;
  scrollbar-width: thin;
  padding: 15px !important;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  margin-bottom: 25px;
  position: sticky;
  top: 100px;
}

/* 目錄標題樣式 */
.toc-title, .td-toc-title, .TableOfContents-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 8px;
}

/* 目錄列表樣式 */
.toc ul, .td-toc ul, .TableOfContents ul, #TableOfContents ul {
  padding-left: 1rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  list-style-type: none;
}

/* 目錄項目樣式 */
.toc a, .td-toc a, .TableOfContents a, #TableOfContents a {
  display: block;
  padding: 4px 0;
  color: #495057;
  text-decoration: none;
  border-radius: 3px;
  font-size: 14px;
  line-height: 1.4;
  transition: all 0.2s ease;
}

/* 目錄項目懸停效果 */
.toc a:hover, .td-toc a:hover, .TableOfContents a:hover, #TableOfContents a:hover {
  color: #0366d6;
  background-color: #e9ecef;
  padding-left: 5px;
}

/* 當前活動項目樣式 */
.toc a.active, .td-toc a.active, .TableOfContents a.active, #TableOfContents a.active {
  color: #0366d6;
  font-weight: 500;
  background-color: rgba(3, 102, 214, 0.1);
  border-left: 3px solid #0366d6;
  padding-left: 8px;
}

/* 滾動條樣式 */
.td-toc::-webkit-scrollbar, 
.toc::-webkit-scrollbar, 
.TableOfContents::-webkit-scrollbar, 
#TableOfContents::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.td-toc::-webkit-scrollbar-thumb, 
.toc::-webkit-scrollbar-thumb, 
.TableOfContents::-webkit-scrollbar-thumb, 
#TableOfContents::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 10px;
}

/* 響應式調整 */
@media (max-width: 768px) {
  .td-toc, .toc, .TableOfContents, #TableOfContents {
    max-height: 50vh !important;
    position: relative;
    top: 0;
    margin-top: 20px;
  }
}
