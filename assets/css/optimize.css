/* 圖片延遲載入相關優化 */
img.lazy {
  opacity: 0;
  transition: opacity 0.3s;
}

img.lazy.loaded {
  opacity: 1;
}

/* 優化字體載入 - 防止字體閃爍 */
.wf-loading h1, 
.wf-loading h2, 
.wf-loading h3, 
.wf-loading h4, 
.wf-loading h5, 
.wf-loading h6, 
.wf-loading p {
  opacity: 0;
}

.wf-active h1, 
.wf-active h2, 
.wf-active h3, 
.wf-active h4, 
.wf-active h5, 
.wf-active h6, 
.wf-active p {
  opacity: 1;
  transition: opacity 0.3s;
}

/* 優化頁面載入動畫 - 減少 CLS (Cumulative Layout Shift) */
.article-card, 
.widget, 
.hero-section {
  min-height: 100px; /* 預留空間避免內容載入時的跳動 */
}

/* 預設圖片容器尺寸以避免 CLS */
.featured-post-slider .post-thumb,
.post-thumb {
  aspect-ratio: 16/9;
  display: block;
  background-color: #f8f8f8;
}

/* 加速頁面過渡動畫 */
.page-transition {
  transition: all 0.3s cubic-bezier(0.77, 0, 0.175, 1);
}
