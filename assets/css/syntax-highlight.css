/* 語法高亮基本樣式 - One Dark 風格 */
.highlight {
  position: relative;
  margin: 1em 0;
  border-radius: 6px;
  background-color: #282c34;
  overflow: auto;
  color: #abb2bf;
}

/* 程式碼區塊樣式 */
.highlight pre {
  padding: 1em;
  margin: 0;
  overflow: auto;
  font-family: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 0.9em;
  line-height: 1.5;
  tab-size: 2;
}

/* 程式碼語言標籤 */
.highlight pre::before {
  content: attr(data-lang);
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.2em 0.5em;
  font-size: 0.75em;
  background: transparent;
  color: #abb2bf;
  border-bottom-left-radius: 4px;
  font-weight: bold;
  text-transform: uppercase;
}

/* 行號樣式 */
.highlight .ln {
  margin-right: 0.8em;
  padding-right: 0.8em;
  color: #5c6370;
  border-right: 1px solid #3a3f4b;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* One Dark 語法高亮主題 */

/* 關鍵字 */
.highlight .k, .highlight .kd, .highlight .kn, .highlight .kp, .highlight .kr, .highlight .kt {
  color: #c678dd;
  font-weight: normal;
}

/* 字串 */
.highlight .s, .highlight .sa, .highlight .sb, .highlight .sc, .highlight .sd, .highlight .s2, .highlight .se, .highlight .sh, .highlight .si, .highlight .sx, .highlight .sr, .highlight .s1, .highlight .ss {
  color: #98c379;
}

/* 數字 */
.highlight .m, .highlight .mb, .highlight .mf, .highlight .mh, .highlight .mi, .highlight .mo {
  color: #d19a66;
}

/* 註解 */
.highlight .c, .highlight .ch, .highlight .cm, .highlight .c1, .highlight .cs {
  color: #5c6370;
  font-style: italic;
}

/* 函數名稱 */
.highlight .nf, .highlight .fm {
  color: #61afef;
}

/* 類型名稱 */
.highlight .nc, .highlight .nn {
  color: #e5c07b;
}

/* 變數名稱 */
.highlight .n, .highlight .nv, .highlight .vc, .highlight .vg, .highlight .vi, .highlight .vm {
  color: #abb2bf;
}

/* 運算符 */
.highlight .o, .highlight .ow {
  color: #56b6c2;
}

/* HTML/XML 標籤 */
.highlight .nt {
  color: #e06c75;
}

/* HTML/XML 屬性 */
.highlight .na {
  color: #d19a66;
}

/* 特定程式語言自定義樣式 */

/* C# 特定樣式 */
.language-csharp .highlight .k, 
.language-csharp .highlight .kt {
  color: #c678dd;
}

.language-csharp .highlight .s {
  color: #98c379;
}

/* JavaScript/TypeScript 特定樣式 */
.language-javascript .highlight .k,
.language-typescript .highlight .k {
  color: #c678dd;
}

.language-javascript .highlight .nx,
.language-typescript .highlight .nx {
  color: #61afef;
}

/* CSS 特定樣式 */
.language-css .highlight .k {
  color: #c678dd;
}

.language-css .highlight .nt {
  color: #e06c75;
}

/* Python 特定樣式 */
.language-python .highlight .k {
  color: #c678dd;
}

.language-python .highlight .nf {
  color: #61afef;
}

/* 程式碼塊懸停效果 */
.highlight:hover {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
}

/* 程式碼塊複製按鈕 (與 code-copy.js 配合使用) */
.copy-code {
  position: absolute;
  top: 0.5em;
  right: 0.5em;
  background-color: #3a404b;
  color: #abb2bf;
  border: 1px solid #4a5261;
  border-radius: 4px;
  padding: 0.2em 0.5em;
  font-size: 0.8em;
  cursor: pointer;
  display: none;
}

.highlight:hover .copy-code {
  display: block;
}

.copy-code:hover {
  background-color: #4a5261;
}

.copy-code:active {
  background-color: #5a6271;
}

/* 複製成功提示 */
.copy-success {
  position: absolute;
  top: 0.5em;
  right: 2.5em;
  background-color: #98c379;
  color: #282c34;
  border-radius: 4px;
  padding: 0.2em 0.5em;
  font-size: 0.8em;
  display: none;
}

/* 淺色模式支援 - GitHub 風格 */
@media (prefers-color-scheme: light) {
  .highlight {
    background-color: #f6f8fa;
    color: #24292e;
  }
  
  .highlight pre::before {
    background: transparent;
    color: #24292e;
  }
  
  .highlight .ln {
    color: #959da5;
    border-right: 1px solid #e1e4e8;
  }
  
  /* 淺色模式下的語法高亮 - GitHub 主題 */
  .highlight .k, .highlight .kd, .highlight .kn, .highlight .kp, .highlight .kr, .highlight .kt {
    color: #d73a49;
    font-weight: normal;
  }
  
  .highlight .s, .highlight .sa, .highlight .sb, .highlight .sc, .highlight .sd, .highlight .s2, .highlight .se, .highlight .sh, .highlight .si, .highlight .sx, .highlight .sr, .highlight .s1, .highlight .ss {
    color: #032f62;
  }
  
  .highlight .c, .highlight .ch, .highlight .cm, .highlight .c1, .highlight .cs {
    color: #6a737d;
    font-style: italic;
  }
  
  .highlight .nf, .highlight .fm {
    color: #6f42c1;
  }
  
  .highlight .nc, .highlight .nn {
    color: #6f42c1;
  }
  
  .highlight .nt {
    color: #22863a;
  }
  
  .highlight .na {
    color: #005cc5;
  }
  
  .highlight .m, .highlight .mb, .highlight .mf, .highlight .mh, .highlight .mi, .highlight .mo {
    color: #005cc5;
  }
  
  /* 淺色模式下的按鈕樣式 */
  .copy-code {
    background-color: #f1f1f1;
    color: #24292e;
    border-color: #d1d5da;
  }
  
  .copy-code:hover {
    background-color: #e1e4e8;
  }
  
  .copy-code:active {
    background-color: #d1d5da;
  }
  
  .copy-success {
    background-color: #28a745;
    color: #fff;
  }
}
