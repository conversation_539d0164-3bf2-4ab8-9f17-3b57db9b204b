<h1 align=center><PERSON><PERSON> Hugo</h1>

<p align=center><PERSON><PERSON> is a gorgeous blog theme that is ideal for representing your personal blog website.  </p>
<h2 align="center"> <a target="_blank" href="https://demo.gethugothemes.com/parsa" rel="nofollow">👀Demo</a> | <a  target="_blank" href="https://pagespeed.web.dev/report?url=https%3A%2F%2Fdemo.gethugothemes.com%2Fparsa%2Fsite%2F&form_factor=desktopF">Page Speed (91%)🚀</a></h2>



<p align=center>
  <a href="https://github.com/gohugoio/hugo/releases/tag/v0.147.2" alt="Contributors">
    <img src="https://img.shields.io/static/v1?label=min-HUGO-version&message=0.147.2&color=f00&logo=hugo" />
  </a>

  <a href="https://github.com/themefisher/parsa-hugo/blob/master/LICENSE">
    <img src="https://img.shields.io/github/license/themefisher/parsa-hugo" alt="license"></a>

  <img src="https://img.shields.io/github/languages/code-size/themefisher/parsa-hugo" alt="code size">

  <a href="https://github.com/themefisher/parsa-hugo/graphs/contributors">
    <img src="https://img.shields.io/github/contributors/themefisher/parsa-hugo" alt="contributors"></a>

  <a href="https://twitter.com/intent/follow?screen_name=gethugothemes">
    <img src="https://img.shields.io/twitter/follow/gethugothemes?style=social&logo=twitter"
      alt="follow on Twitter"></a>
</p>

---

<p align="center">
<img src="https://demo.gethugothemes.com/thumbnails/parsa.png" alt="screenshot" width="100%">
</p>

---

## 🔑Key Features
- 📄 8+ Pre-designed pages
- 📊 Google Analytics Support
- 📦 CSS and JS Bundle with Hugo Pipe
- ⚙️ Netlify Settings Predefined
- 📬 Contact Form Support
- 🔍 Search by Fuse.js
- 📸 Instagram Feed Available
- 🔒 GDPR Consent Enabled
- ⏱️ Post Reading Time Calculator
- 🧱 Masonry Support
- 👥 Multiple Author and Single Author Available
- ⚡ Google Page Speed Optimized
- 🌐 Open Graph Meta Tag
- 🐦 Twitter Card Meta Tag

## 📄 8+ Pre-designed pages
- 🏠Multi Home Page
- 👤About
- 📞Contact
- 📝 Blog Pages
- 📄 Blog Single Pages
- 🗂️ Category Page
- 📄 Category Single Page
- 🔖 Tag Pag

## 🔧Local development

```bash
# clone the repository
<NAME_EMAIL>:themefisher/parsa-hugo.git

# setup project
$ npm run project-setup

# Start local dev server
$ npm run dev
```

Or Check out [Full Documentation](https://docs.gethugothemes.com/parsa/?ref=github).


## ⚙️Deployment and hosting

[![Deploy to
Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/themefisher/parsa-hugo)

Follow the steps.

<!-- reporting issue -->
## 🐞Reporting Issues

We use GitHub Issues as the official bug tracker for the Parsa Template. Please Search [existing
issues](https://github.com/themefisher/parsa-hugo/issues). Someone may have already reported the same problem.
If your problem or idea has not been addressed yet, feel free to [open a new
issue](https://github.com/themefisher/parsa-hugo/issues).

## 📱Submit Your Website To Our Showcase

Are you using Parsa Hugo theme? Submit it to our [showcase](https://gethugothemes.com/showcase). 

Our showcase aims to demonstrate to the world what amazing websites people like you have created utilizing our Hugo themes and to show that Hugo has tremendous capabilities as a Static Site Generator. 

[Submit](https://gethugothemes.com/showcase?submit=show) your Parsa Hugo powered website.

<!-- licence -->
## 📄License

Copyright &copy; Designed by [Themefisher](https://themefisher.com) & Developed by
[Gethugothemes](https://gethugothemes.com)

**Code License:** Released under the [MIT](https://github.com/themefisher/parsa-hugo/blob/master/LICENSE) license.

**Image license:** The images are only for demonstration purposes. They have their licenses. We don't have permission to
share those images.

<!-- resources -->
## 🙏Special Thanks

- [Bootstrap](https://getbootstrap.com)
- [Jquery](https://jquery.com)
- [Themify Icons](https://themify.me/themify-icons)
- [Slick Slider](https://kenwheeler.github.io/slick/)
- [Headroom](https://wicky.nillia.ms/headroom.js)
- [Instafeed](https://instafeedjs.com/)
- [Masonry](https://masonry.desandro.com)
- [Fuse Js](https://fusejs.io)
- [Mark Js](https://markjs.io/)
- [Google Fonts](https://fonts.google.com/)
- [All Contributors](https://github.com/themefisher/parsa-hugo/graphs/contributors)

## 👨‍💻Hire Us

Besides developing unique, blazing-fast Hugo themes, we also provide customized services. We specialize in creating affordable, high-quality static websites based on Hugo.

If you need to customize the theme or complete website development from scratch, you can hire us. **Check Our
[services](https://gethugothemes.com/services/?utm_source=parsa_github&utm_medium=referral&utm_campaign=github_theme_readme)**

<!-- premium themes -->
## 💎Premium Themes By Us

| [![Mega-Bundle-HUGO](https://demo.gethugothemes.com/thumbnails/bundle.png?)](https://gethugothemes.com/bundle/?utm_source=parsa_github&utm_medium=referral&utm_campaign=github_theme_readme) | [![Reader](https://demo.gethugothemes.com/thumbnails/reader.png)](https://gethugothemes.com/products/reader/) | [![logbook](https://demo.gethugothemes.com/thumbnails/logbook.png)](https://gethugothemes.com/products/logbook/) |
|:---:|:---:|:---:|
| **Get 55+ Premium Hugo Themes Bundle** | **Reader** | **Logbook** |
