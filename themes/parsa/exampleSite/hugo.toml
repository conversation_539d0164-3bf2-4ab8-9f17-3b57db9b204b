# default config
baseurl = "https://demo.gethugothemes.com/parsa/site/"
languageCode = "en-us"
title = "Parsa Hugo | Personal Blog Template"
theme = "parsa-hugo"
summaryLength = "10"
pagination.pagerSize = 6
# google analytics
googleAnalytics = "" # example : UA-123-45

# output
[outputs]
home = ["HTML", "RSS", "JSON"]

[markup.goldmark.renderer]
unsafe = true

# CSS Plugins
[[params.plugins.css]]
link = "plugins/bootstrap/bootstrap.min.css"
[[params.plugins.css]]
link = "plugins/slick/slick.css"
[[params.plugins.css]]
link = "plugins/themify-icons/themify-icons.css"

# JS Plugins
[[params.plugins.js]]
link = "plugins/jQuery/jquery.min.js"
[[params.plugins.js]]
link = "plugins/bootstrap/bootstrap.min.js"
[[params.plugins.js]]
link = "plugins/slick/slick.min.js"
[[params.plugins.js]]
link = "plugins/headroom/headroom.js"
[[params.plugins.js]]
link = "plugins/instafeed/instafeed.min.js"
[[params.plugins.js]]
link = "plugins/masonry/masonry.js"
[[params.plugins.js]]
link = "plugins/reading-time/readingTime.min.js"
[[params.plugins.js]]
link = "plugins/smooth-scroll/smooth-scroll.js"
[[params.plugins.js]]
link = "plugins/search/fuse.min.js"
[[params.plugins.js]]
link = "plugins/search/mark.js"
[[params.plugins.js]]
link = "plugins/search/search.js"


# deult parameter
[params]
logo = "images/logo.png"
home = "Home"
# theme layout ( two layout is available, "1" and "2" )
layout = "1"
# Meta data
description = "This is meta description"
# Preloader
preloader = true
# search
search = true
# about author
author = "John Doe"
author_image = "images/banner-img.png"
bio = "I’m a Freelance Interactive Art Director based in France. Focusing across branding and identity, digital and print."
# Contact info
mobile = "+90 000 333 22"
email = "<EMAIL>"
address = "6 rip carl Avenue CA 90733"
# Contact Form Action
contact_form_action = "#"
# copyright
copyright = "Copyright &copy; 2023 a theme by [themefisher.com](https://themefisher.com)"

# Instagram feed
[params.instafeed]
enable = true
access_token = "IGQVJYeUk4YWNIY1h4OWZANeS1wRHZARdjJ5QmdueXN2RFR6NF9iYUtfcGp1NmpxZA3RTbnU1MXpDNVBHTzZAMOFlxcGlkVHBKdjhqSnUybERhNWdQSE5hVmtXT013MEhOQVJJRGJBRURn"

# cookies
[params.cookies]
enable = true
expire_days = 2

# social site
[[params.social]]
icon = "ti-facebook"
link = "https://facebook.com/themefisher"

[[params.social]]
icon = "ti-twitter-alt"
link = "#"

[[params.social]]
icon = "ti-linkedin"
link = "#"

[[params.social]]
icon = "ti-github"
link = "#"
