name = "<PERSON><PERSON>"
license = "MIT"
licenselink = "https://github.com/themefisher/parsa-hugo/blob/master/LICENSE"
description = "Parsa is a personal blog theme powered by <PERSON>. It also can be used for portfolio website."
homepage = "https://gethugothemes.com/products/parsa-hugo-theme/"
demosite = "https://demo.gethugothemes.com/parsa/"
tags = ['landing-page', 'fast', 'light', 'white', 'modern', 'themefisher', 'hugo-theme', 'hugo-templates', 'bootstrap', 'blog', 'responsive', 'clean', 'simple', 'creative', 'contact-form', 'custom-themes', 'mobile', 'minimalistic', 'pages', 'resume', 'portfolio', 'google analytics', 'customizable', 'flexbox', 'presentation', 'product', 'theme', 'typography']
features = ['bootstrap','responsive']
min_version = "0.147.2"

[author]
  name = "Themefisher"
  homepage = "https://themefisher.com/"
