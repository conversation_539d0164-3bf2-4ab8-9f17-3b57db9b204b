!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.Popper=e()}(this,function(){"use strict";function t(t){return t&&"[object Function]"==={}.toString.call(t)}function e(t,e){if(1!==t.nodeType)return[];var n=getComputedStyle(t,null);return e?n[e]:n}function n(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function i(t){if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}var r=e(t),o=r.overflow,s=r.overflowX,a=r.overflowY;return/(auto|scroll|overlay)/.test(o+a+s)?t:i(n(t))}function r(t){return 11===t?K:10===t?V:K||V}function o(t){if(!t)return document.documentElement;for(var n=r(10)?document.body:null,i=t.offsetParent;i===n&&t.nextElementSibling;)i=(t=t.nextElementSibling).offsetParent;var s=i&&i.nodeName;return s&&"BODY"!==s&&"HTML"!==s?-1!==["TD","TABLE"].indexOf(i.nodeName)&&"static"===e(i,"position")?o(i):i:t?t.ownerDocument.documentElement:document.documentElement}function s(t){return null===t.parentNode?t:s(t.parentNode)}function a(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return document.documentElement;var n=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,i=n?t:e,r=n?e:t,l=document.createRange();l.setStart(i,0),l.setEnd(r,0);var c,h,f=l.commonAncestorContainer;if(t!==f&&e!==f||i.contains(r))return c=f,h=c.nodeName,"BODY"===h||"HTML"!==h&&o(c.firstElementChild)!==c?o(f):f;var u=s(t);return u.host?a(u.host,e):a(t,s(e).host)}function l(t){var e="top"===(1<arguments.length&&void 0!==arguments[1]?arguments[1]:"top")?"scrollTop":"scrollLeft",n=t.nodeName;if("BODY"===n||"HTML"===n){var i=t.ownerDocument.documentElement;return(t.ownerDocument.scrollingElement||i)[e]}return t[e]}function c(t,e){var n="x"===e?"Left":"Top",i="Left"==n?"Right":"Bottom";return parseFloat(t["border"+n+"Width"],10)+parseFloat(t["border"+i+"Width"],10)}function h(t,e,n,i){return H(e["offset"+t],e["scroll"+t],n["client"+t],n["offset"+t],n["scroll"+t],r(10)?n["offset"+t]+i["margin"+("Height"===t?"Top":"Left")]+i["margin"+("Height"===t?"Bottom":"Right")]:0)}function f(){var t=document.body,e=document.documentElement,n=r(10)&&getComputedStyle(e);return{height:h("Height",t,e,n),width:h("Width",t,e,n)}}function u(t){return q({},t,{right:t.left+t.width,bottom:t.top+t.height})}function d(t){var n={};try{if(r(10)){n=t.getBoundingClientRect();var i=l(t,"top"),o=l(t,"left");n.top+=i,n.left+=o,n.bottom+=i,n.right+=o}else n=t.getBoundingClientRect()}catch(t){}var s={left:n.left,top:n.top,width:n.right-n.left,height:n.bottom-n.top},a="HTML"===t.nodeName?f():{},h=a.width||t.clientWidth||s.right-s.left,d=a.height||t.clientHeight||s.bottom-s.top,p=t.offsetWidth-h,g=t.offsetHeight-d;if(p||g){var m=e(t);p-=c(m,"x"),g-=c(m,"y"),s.width-=p,s.height-=g}return u(s)}function p(t,n){var o=2<arguments.length&&void 0!==arguments[2]&&arguments[2],s=r(10),a="HTML"===n.nodeName,c=d(t),h=d(n),f=i(t),p=e(n),g=parseFloat(p.borderTopWidth,10),m=parseFloat(p.borderLeftWidth,10);o&&"HTML"===n.nodeName&&(h.top=H(h.top,0),h.left=H(h.left,0));var _=u({top:c.top-h.top-g,left:c.left-h.left-m,width:c.width,height:c.height});if(_.marginTop=0,_.marginLeft=0,!s&&a){var v=parseFloat(p.marginTop,10),E=parseFloat(p.marginLeft,10);_.top-=g-v,_.bottom-=g-v,_.left-=m-E,_.right-=m-E,_.marginTop=v,_.marginLeft=E}return(s&&!o?n.contains(f):n===f&&"BODY"!==f.nodeName)&&(_=function(t,e){var n=2<arguments.length&&void 0!==arguments[2]&&arguments[2],i=l(e,"top"),r=l(e,"left"),o=n?-1:1;return t.top+=i*o,t.bottom+=i*o,t.left+=r*o,t.right+=r*o,t}(_,n)),_}function g(t){if(!t||!t.parentElement||r())return document.documentElement;for(var n=t.parentElement;n&&"none"===e(n,"transform");)n=n.parentElement;return n||document.documentElement}function m(t,r,o,s){var c=4<arguments.length&&void 0!==arguments[4]&&arguments[4],h={top:0,left:0},d=c?g(t):a(t,r);if("viewport"===s)h=function(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=t.ownerDocument.documentElement,i=p(t,n),r=H(n.clientWidth,window.innerWidth||0),o=H(n.clientHeight,window.innerHeight||0),s=e?0:l(n),a=e?0:l(n,"left");return u({top:s-i.top+i.marginTop,left:a-i.left+i.marginLeft,width:r,height:o})}(d,c);else{var m;"scrollParent"===s?"BODY"===(m=i(n(r))).nodeName&&(m=t.ownerDocument.documentElement):m="window"===s?t.ownerDocument.documentElement:s;var _=p(m,d,c);if("HTML"!==m.nodeName||function t(i){var r=i.nodeName;return"BODY"!==r&&"HTML"!==r&&("fixed"===e(i,"position")||t(n(i)))}(d))h=_;else{var v=f(),E=v.height,y=v.width;h.top+=_.top-_.marginTop,h.bottom=E+_.top,h.left+=_.left-_.marginLeft,h.right=y+_.left}}return h.left+=o,h.top+=o,h.right-=o,h.bottom-=o,h}function _(t,e,n,i,r){var o=5<arguments.length&&void 0!==arguments[5]?arguments[5]:0;if(-1===t.indexOf("auto"))return t;var s=m(n,i,o,r),a={top:{width:s.width,height:e.top-s.top},right:{width:s.right-e.right,height:s.height},bottom:{width:s.width,height:s.bottom-e.bottom},left:{width:e.left-s.left,height:s.height}},l=Object.keys(a).map(function(t){return q({key:t},a[t],{area:(e=a[t],e.width*e.height)});var e}).sort(function(t,e){return e.area-t.area}),c=l.filter(function(t){var e=t.width,i=t.height;return e>=n.clientWidth&&i>=n.clientHeight}),h=0<c.length?c[0].key:l[0].key,f=t.split("-")[1];return h+(f?"-"+f:"")}function v(t,e,n){var i=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return p(n,i?g(e):a(e,n),i)}function E(t){var e=getComputedStyle(t),n=parseFloat(e.marginTop)+parseFloat(e.marginBottom),i=parseFloat(e.marginLeft)+parseFloat(e.marginRight);return{width:t.offsetWidth+i,height:t.offsetHeight+n}}function y(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,function(t){return e[t]})}function b(t,e,n){n=n.split("-")[0];var i=E(t),r={width:i.width,height:i.height},o=-1!==["right","left"].indexOf(n),s=o?"top":"left",a=o?"left":"top",l=o?"height":"width",c=o?"width":"height";return r[s]=e[s]+e[l]/2-i[l]/2,r[a]=n===a?e[a]-i[c]:e[y(a)],r}function T(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function C(e,n,i){return(void 0===i?e:e.slice(0,function(t,e,n){if(Array.prototype.findIndex)return t.findIndex(function(t){return t[e]===n});var i=T(t,function(t){return t[e]===n});return t.indexOf(i)}(e,"name",i))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var i=e.function||e.fn;e.enabled&&t(i)&&(n.offsets.popper=u(n.offsets.popper),n.offsets.reference=u(n.offsets.reference),n=i(n,e))}),n}function w(t,e){return t.some(function(t){var n=t.name;return t.enabled&&n===e})}function I(t){for(var e=[!1,"ms","Webkit","Moz","O"],n=t.charAt(0).toUpperCase()+t.slice(1),i=0;i<e.length;i++){var r=e[i],o=r?""+r+n:t;if(void 0!==document.body.style[o])return o}return null}function D(t){var e=t.ownerDocument;return e?e.defaultView:window}function A(t,e,n,r){n.updateBound=r,D(t).addEventListener("resize",n.updateBound,{passive:!0});var o=i(t);return function t(e,n,r,o){var s="BODY"===e.nodeName,a=s?e.ownerDocument.defaultView:e;a.addEventListener(n,r,{passive:!0}),s||t(i(a.parentNode),n,r,o),o.push(a)}(o,"scroll",n.updateBound,n.scrollParents),n.scrollElement=o,n.eventsEnabled=!0,n}function S(){var t,e;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(t=this.reference,e=this.state,D(t).removeEventListener("resize",e.updateBound),e.scrollParents.forEach(function(t){t.removeEventListener("scroll",e.updateBound)}),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e))}function O(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function N(t,e){Object.keys(e).forEach(function(n){var i="";-1!==["width","height","top","right","bottom","left"].indexOf(n)&&O(e[n])&&(i="px"),t.style[n]=e[n]+i})}function k(t,e,n){var i=T(t,function(t){return t.name===e}),r=!!i&&t.some(function(t){return t.name===n&&t.enabled&&t.order<i.order});if(!r){var o="`"+e+"`";console.warn("`"+n+"` modifier is required by "+o+" modifier in order to work, be sure to include it before "+o+"!")}return r}function L(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=X.indexOf(t),i=X.slice(n+1).concat(X.slice(0,n));return e?i.reverse():i}function P(t,e,n,i){var r=[0,0],o=-1!==["right","left"].indexOf(i),s=t.split(/(\+|\-)/).map(function(t){return t.trim()}),a=s.indexOf(T(s,function(t){return-1!==t.search(/,|\s/)}));s[a]&&-1===s[a].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/,c=-1===a?[s]:[s.slice(0,a).concat([s[a].split(l)[0]]),[s[a].split(l)[1]].concat(s.slice(a+1))];return(c=c.map(function(t,i){var r=(1===i?!o:o)?"height":"width",s=!1;return t.reduce(function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,s=!0,t):s?(t[t.length-1]+=e,s=!1,t):t.concat(e)},[]).map(function(t){return function(t,e,n,i){var r=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+r[1],s=r[2];if(!o)return t;if(0===s.indexOf("%")){var a;switch(s){case"%p":a=n;break;case"%":case"%r":default:a=i}return u(a)[e]/100*o}return"vh"===s||"vw"===s?("vh"===s?H(document.documentElement.clientHeight,window.innerHeight||0):H(document.documentElement.clientWidth,window.innerWidth||0))/100*o:o}(t,r,e,n)})})).forEach(function(t,e){t.forEach(function(n,i){O(n)&&(r[e]+=n*("-"===t[i-1]?-1:1))})}),r}for(var x=Math.min,j=Math.round,R=Math.floor,H=Math.max,F="undefined"!=typeof window&&"undefined"!=typeof document,W=["Edge","Trident","Firefox"],M=0,U=0;U<W.length;U+=1)if(F&&0<=navigator.userAgent.indexOf(W[U])){M=1;break}var B=F&&window.Promise?function(t){var e=!1;return function(){e||(e=!0,window.Promise.resolve().then(function(){e=!1,t()}))}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout(function(){e=!1,t()},M))}},K=F&&!(!window.MSInputMethodContext||!document.documentMode),V=F&&/MSIE 10/.test(navigator.userAgent),Q=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},Y=function(){function t(t,e){for(var n,i=0;i<e.length;i++)n=e[i],n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}return function(e,n,i){return n&&t(e.prototype,n),i&&t(e,i),e}}(),G=function(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t},q=Object.assign||function(t){for(var e,n=1;n<arguments.length;n++)for(var i in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},z=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],X=z.slice(3),J="flip",Z="clockwise",$="counterclockwise",tt=function(){function e(n,i){var r=this,o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};Q(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(r.update)},this.update=B(this.update.bind(this)),this.options=q({},e.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=n&&n.jquery?n[0]:n,this.popper=i&&i.jquery?i[0]:i,this.options.modifiers={},Object.keys(q({},e.Defaults.modifiers,o.modifiers)).forEach(function(t){r.options.modifiers[t]=q({},e.Defaults.modifiers[t]||{},o.modifiers?o.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(t){return q({name:t},r.options.modifiers[t])}).sort(function(t,e){return t.order-e.order}),this.modifiers.forEach(function(e){e.enabled&&t(e.onLoad)&&e.onLoad(r.reference,r.popper,r.options,e,r.state)}),this.update();var s=this.options.eventsEnabled;s&&this.enableEventListeners(),this.state.eventsEnabled=s}return Y(e,[{key:"update",value:function(){return function(){if(!this.state.isDestroyed){var t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};t.offsets.reference=v(this.state,this.popper,this.reference,this.options.positionFixed),t.placement=_(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.positionFixed=this.options.positionFixed,t.offsets.popper=b(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",t=C(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t))}}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,w(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[I("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=A(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return S.call(this)}}]),e}();return tt.Utils=("undefined"==typeof window?global:window).PopperUtils,tt.placements=z,tt.Defaults={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e=t.placement,n=e.split("-")[0],i=e.split("-")[1];if(i){var r=t.offsets,o=r.reference,s=r.popper,a=-1!==["bottom","top"].indexOf(n),l=a?"left":"top",c=a?"width":"height",h={start:G({},l,o[l]),end:G({},l,o[l]+o[c]-s[c])};t.offsets.popper=q({},s,h[i])}return t}},offset:{order:200,enabled:!0,fn:function(t,e){var n,i=e.offset,r=t.placement,o=t.offsets,s=o.popper,a=o.reference,l=r.split("-")[0];return n=O(+i)?[+i,0]:P(i,s,a,l),"left"===l?(s.top+=n[0],s.left-=n[1]):"right"===l?(s.top+=n[0],s.left+=n[1]):"top"===l?(s.left+=n[0],s.top-=n[1]):"bottom"===l&&(s.left+=n[0],s.top+=n[1]),t.popper=s,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,e){var n=e.boundariesElement||o(t.instance.popper);t.instance.reference===n&&(n=o(n));var i=I("transform"),r=t.instance.popper.style,s=r.top,a=r.left,l=r[i];r.top="",r.left="",r[i]="";var c=m(t.instance.popper,t.instance.reference,e.padding,n,t.positionFixed);r.top=s,r.left=a,r[i]=l,e.boundaries=c;var h=e.priority,f=t.offsets.popper,u={primary:function(t){var n=f[t];return f[t]<c[t]&&!e.escapeWithReference&&(n=H(f[t],c[t])),G({},t,n)},secondary:function(t){var n="right"===t?"left":"top",i=f[n];return f[t]>c[t]&&!e.escapeWithReference&&(i=x(f[n],c[t]-("right"===t?f.width:f.height))),G({},n,i)}};return h.forEach(function(t){var e=-1===["left","top"].indexOf(t)?"secondary":"primary";f=q({},f,u[e](t))}),t.offsets.popper=f,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,n=e.popper,i=e.reference,r=t.placement.split("-")[0],o=R,s=-1!==["top","bottom"].indexOf(r),a=s?"right":"bottom",l=s?"left":"top",c=s?"width":"height";return n[a]<o(i[l])&&(t.offsets.popper[l]=o(i[l])-n[c]),n[l]>o(i[a])&&(t.offsets.popper[l]=o(i[a])),t}},arrow:{order:500,enabled:!0,fn:function(t,n){var i;if(!k(t.instance.modifiers,"arrow","keepTogether"))return t;var r=n.element;if("string"==typeof r){if(!(r=t.instance.popper.querySelector(r)))return t}else if(!t.instance.popper.contains(r))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var o=t.placement.split("-")[0],s=t.offsets,a=s.popper,l=s.reference,c=-1!==["left","right"].indexOf(o),h=c?"height":"width",f=c?"Top":"Left",d=f.toLowerCase(),p=c?"left":"top",g=c?"bottom":"right",m=E(r)[h];l[g]-m<a[d]&&(t.offsets.popper[d]-=a[d]-(l[g]-m)),l[d]+m>a[g]&&(t.offsets.popper[d]+=l[d]+m-a[g]),t.offsets.popper=u(t.offsets.popper);var _=l[d]+l[h]/2-m/2,v=e(t.instance.popper),y=parseFloat(v["margin"+f],10),b=parseFloat(v["border"+f+"Width"],10),T=_-t.offsets.popper[d]-y-b;return T=H(x(a[h]-m,T),0),t.arrowElement=r,t.offsets.arrow=(G(i={},d,j(T)),G(i,p,""),i),t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(t,e){if(w(t.instance.modifiers,"inner"))return t;if(t.flipped&&t.placement===t.originalPlacement)return t;var n=m(t.instance.popper,t.instance.reference,e.padding,e.boundariesElement,t.positionFixed),i=t.placement.split("-")[0],r=y(i),o=t.placement.split("-")[1]||"",s=[];switch(e.behavior){case J:s=[i,r];break;case Z:s=L(i);break;case $:s=L(i,!0);break;default:s=e.behavior}return s.forEach(function(a,l){if(i!==a||s.length===l+1)return t;i=t.placement.split("-")[0],r=y(i);var c,h=t.offsets.popper,f=t.offsets.reference,u=R,d="left"===i&&u(h.right)>u(f.left)||"right"===i&&u(h.left)<u(f.right)||"top"===i&&u(h.bottom)>u(f.top)||"bottom"===i&&u(h.top)<u(f.bottom),p=u(h.left)<u(n.left),g=u(h.right)>u(n.right),m=u(h.top)<u(n.top),_=u(h.bottom)>u(n.bottom),v="left"===i&&p||"right"===i&&g||"top"===i&&m||"bottom"===i&&_,E=-1!==["top","bottom"].indexOf(i),T=!!e.flipVariations&&(E&&"start"===o&&p||E&&"end"===o&&g||!E&&"start"===o&&m||!E&&"end"===o&&_);(d||v||T)&&(t.flipped=!0,(d||v)&&(i=s[l+1]),T&&(o="end"===(c=o)?"start":"start"===c?"end":c),t.placement=i+(o?"-"+o:""),t.offsets.popper=q({},t.offsets.popper,b(t.instance.popper,t.offsets.reference,t.placement)),t=C(t.instance.modifiers,t,"flip"))}),t},behavior:"flip",padding:5,boundariesElement:"viewport"},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,n=e.split("-")[0],i=t.offsets,r=i.popper,o=i.reference,s=-1!==["left","right"].indexOf(n),a=-1===["top","left"].indexOf(n);return r[s?"left":"top"]=o[n]-(a?r[s?"width":"height"]:0),t.placement=y(e),t.offsets.popper=u(r),t}},hide:{order:800,enabled:!0,fn:function(t){if(!k(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,n=T(t.instance.modifiers,function(t){return"preventOverflow"===t.name}).boundaries;if(e.bottom<n.top||e.left>n.right||e.top>n.bottom||e.right<n.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var n=e.x,i=e.y,r=t.offsets.popper,s=T(t.instance.modifiers,function(t){return"applyStyle"===t.name}).gpuAcceleration;void 0!==s&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var a,l,c=void 0===s?e.gpuAcceleration:s,h=d(o(t.instance.popper)),f={position:r.position},u={left:R(r.left),top:j(r.top),bottom:j(r.bottom),right:R(r.right)},p="bottom"===n?"top":"bottom",g="right"===i?"left":"right",m=I("transform");if(l="bottom"==p?-h.height+u.bottom:u.top,a="right"==g?-h.width+u.right:u.left,c&&m)f[m]="translate3d("+a+"px, "+l+"px, 0)",f[p]=0,f[g]=0,f.willChange="transform";else{var _="bottom"==p?-1:1,v="right"==g?-1:1;f[p]=l*_,f[g]=a*v,f.willChange=p+", "+g}var E={"x-placement":t.placement};return t.attributes=q({},E,t.attributes),t.styles=q({},f,t.styles),t.arrowStyles=q({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){return N(t.instance.popper,t.styles),e=t.instance.popper,n=t.attributes,Object.keys(n).forEach(function(t){!1===n[t]?e.removeAttribute(t):e.setAttribute(t,n[t])}),t.arrowElement&&Object.keys(t.arrowStyles).length&&N(t.arrowElement,t.arrowStyles),t;var e,n},onLoad:function(t,e,n,i,r){var o=v(r,e,t,n.positionFixed),s=_(n.placement,o,e,t,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return e.setAttribute("x-placement",s),N(e,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},tt}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],e):e(t.bootstrap={},t.jQuery,t.Popper)}(this,function(t,e,n){"use strict";function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function r(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function o(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),i.forEach(function(e){var i,r,o;i=t,o=n[r=e],r in i?Object.defineProperty(i,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):i[r]=o})}return t}e=e&&e.hasOwnProperty("default")?e.default:e,n=n&&n.hasOwnProperty("default")?n.default:n;var s,a,l,c,h,f,u,d,p,g,m,_,v,E,y,b,T,C,w,I,D,A,S,O,N,k,L,P,x,j,R,H,F,W,M,U,B,K,V,Q,Y,G,q,z,X,J,Z,$,tt,et,nt,it,rt,ot,st,at,lt,ct,ht,ft,ut,dt,pt,gt,mt,_t,vt,Et,yt,bt,Tt,Ct,wt,It,Dt,At,St,Ot,Nt,kt,Lt,Pt,xt,jt,Rt,Ht,Ft,Wt,Mt,Ut,Bt,Kt,Vt,Qt,Yt,Gt,qt,zt,Xt,Jt,Zt,$t,te,ee,ne,ie,re,oe,se,ae,le,ce,he,fe,ue,de,pe,ge,me,_e,ve,Ee,ye,be,Te,Ce,we,Ie,De,Ae,Se,Oe,Ne,ke,Le,Pe,xe,je,Re,He,Fe,We,Me,Ue,Be,Ke,Ve,Qe,Ye,Ge,qe,ze,Xe,Je,Ze,$e,tn,en,nn,rn=function(t){var e="transitionend";var n={TRANSITION_END:"bsTransitionEnd",getUID:function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},getSelectorFromElement:function(e){var n=e.getAttribute("data-target");n&&"#"!==n||(n=e.getAttribute("href")||"");try{return 0<t(document).find(n).length?n:null}catch(e){return null}},getTransitionDurationFromElement:function(e){if(!e)return 0;var n=t(e).css("transition-duration");return parseFloat(n)?(n=n.split(",")[0],1e3*parseFloat(n)):0},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(n){t(n).trigger(e)},supportsTransitionEnd:function(){return Boolean(e)},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,i){for(var r in i)if(Object.prototype.hasOwnProperty.call(i,r)){var o=i[r],s=e[r],a=s&&n.isElement(s)?"element":(l=s,{}.toString.call(l).match(/\s([a-z]+)/i)[1].toLowerCase());if(!new RegExp(o).test(a))throw new Error(t.toUpperCase()+': Option "'+r+'" provided type "'+a+'" but expected type "'+o+'".')}var l}};return t.fn.emulateTransitionEnd=function(e){var i=this,r=!1;return t(this).one(n.TRANSITION_END,function(){r=!0}),setTimeout(function(){r||n.triggerTransitionEnd(i)},e),this},t.event.special[n.TRANSITION_END]={bindType:e,delegateType:e,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}},n}(e),on=(a="alert",c="."+(l="bs.alert"),h=(s=e).fn[a],f={CLOSE:"close"+c,CLOSED:"closed"+c,CLICK_DATA_API:"click"+c+".data-api"},"alert","fade","show",u=function(){function t(t){this._element=t}var e=t.prototype;return e.close=function(t){var e=this._element;t&&(e=this._getRootElement(t)),this._triggerCloseEvent(e).isDefaultPrevented()||this._removeElement(e)},e.dispose=function(){s.removeData(this._element,l),this._element=null},e._getRootElement=function(t){var e=rn.getSelectorFromElement(t),n=!1;return e&&(n=s(e)[0]),n||(n=s(t).closest(".alert")[0]),n},e._triggerCloseEvent=function(t){var e=s.Event(f.CLOSE);return s(t).trigger(e),e},e._removeElement=function(t){var e=this;if(s(t).removeClass("show"),s(t).hasClass("fade")){var n=rn.getTransitionDurationFromElement(t);s(t).one(rn.TRANSITION_END,function(n){return e._destroyElement(t,n)}).emulateTransitionEnd(n)}else this._destroyElement(t)},e._destroyElement=function(t){s(t).detach().trigger(f.CLOSED).remove()},t._jQueryInterface=function(e){return this.each(function(){var n=s(this),i=n.data(l);i||(i=new t(this),n.data(l,i)),"close"===e&&i[e](this)})},t._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},r(t,null,[{key:"VERSION",get:function(){return"4.1.1"}}]),t}(),s(document).on(f.CLICK_DATA_API,'[data-dismiss="alert"]',u._handleDismiss(new u)),s.fn[a]=u._jQueryInterface,s.fn[a].Constructor=u,s.fn[a].noConflict=function(){return s.fn[a]=h,u._jQueryInterface},u),sn=(p="button",m="."+(g="bs.button"),_=".data-api",v=(d=e).fn[p],E="active","btn",y='[data-toggle^="button"]',b='[data-toggle="buttons"]',"input",T=".active",C=".btn",w={CLICK_DATA_API:"click"+m+_,FOCUS_BLUR_DATA_API:"focus"+m+_+" blur"+m+_},I=function(){function t(t){this._element=t}var e=t.prototype;return e.toggle=function(){var t=!0,e=!0,n=d(this._element).closest(b)[0];if(n){var i=d(this._element).find("input")[0];if(i){if("radio"===i.type)if(i.checked&&d(this._element).hasClass(E))t=!1;else{var r=d(n).find(T)[0];r&&d(r).removeClass(E)}if(t){if(i.hasAttribute("disabled")||n.hasAttribute("disabled")||i.classList.contains("disabled")||n.classList.contains("disabled"))return;i.checked=!d(this._element).hasClass(E),d(i).trigger("change")}i.focus(),e=!1}}e&&this._element.setAttribute("aria-pressed",!d(this._element).hasClass(E)),t&&d(this._element).toggleClass(E)},e.dispose=function(){d.removeData(this._element,g),this._element=null},t._jQueryInterface=function(e){return this.each(function(){var n=d(this).data(g);n||(n=new t(this),d(this).data(g,n)),"toggle"===e&&n[e]()})},r(t,null,[{key:"VERSION",get:function(){return"4.1.1"}}]),t}(),d(document).on(w.CLICK_DATA_API,y,function(t){t.preventDefault();var e=t.target;d(e).hasClass("btn")||(e=d(e).closest(C)),I._jQueryInterface.call(d(e),"toggle")}).on(w.FOCUS_BLUR_DATA_API,y,function(t){var e=d(t.target).closest(C)[0];d(e).toggleClass("focus",/^focus(in)?$/.test(t.type))}),d.fn[p]=I._jQueryInterface,d.fn[p].Constructor=I,d.fn[p].noConflict=function(){return d.fn[p]=v,I._jQueryInterface},I),an=(A="carousel",O="."+(S="bs.carousel"),N=".data-api",k=(D=e).fn[A],L={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0},P={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean"},x="next",j="prev","left","right",R={SLIDE:"slide"+O,SLID:"slid"+O,KEYDOWN:"keydown"+O,MOUSEENTER:"mouseenter"+O,MOUSELEAVE:"mouseleave"+O,TOUCHEND:"touchend"+O,LOAD_DATA_API:"load"+O+N,CLICK_DATA_API:"click"+O+N},H="carousel",F="active","slide",W="carousel-item-right",M="carousel-item-left",U="carousel-item-next",B="carousel-item-prev",K={ACTIVE:".active",ACTIVE_ITEM:".active.carousel-item",ITEM:".carousel-item",NEXT_PREV:".carousel-item-next, .carousel-item-prev",INDICATORS:".carousel-indicators",DATA_SLIDE:"[data-slide], [data-slide-to]",DATA_RIDE:'[data-ride="carousel"]'},V=function(){function t(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this._config=this._getConfig(e),this._element=D(t)[0],this._indicatorsElement=D(this._element).find(K.INDICATORS)[0],this._addEventListeners()}var e=t.prototype;return e.next=function(){this._isSliding||this._slide(x)},e.nextWhenVisible=function(){!document.hidden&&D(this._element).is(":visible")&&"hidden"!==D(this._element).css("visibility")&&this.next()},e.prev=function(){this._isSliding||this._slide(j)},e.pause=function(t){t||(this._isPaused=!0),D(this._element).find(K.NEXT_PREV)[0]&&(rn.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},e.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},e.to=function(t){var e=this;this._activeElement=D(this._element).find(K.ACTIVE_ITEM)[0];var n=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)D(this._element).one(R.SLID,function(){return e.to(t)});else{if(n===t)return this.pause(),void this.cycle();var i=n<t?x:j;this._slide(i,this._items[t])}},e.dispose=function(){D(this._element).off(O),D.removeData(this._element,S),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},e._getConfig=function(t){return t=o({},L,t),rn.typeCheckConfig(A,t,P),t},e._addEventListeners=function(){var t=this;this._config.keyboard&&D(this._element).on(R.KEYDOWN,function(e){return t._keydown(e)}),"hover"===this._config.pause&&(D(this._element).on(R.MOUSEENTER,function(e){return t.pause(e)}).on(R.MOUSELEAVE,function(e){return t.cycle(e)}),"ontouchstart"in document.documentElement&&D(this._element).on(R.TOUCHEND,function(){t.pause(),t.touchTimeout&&clearTimeout(t.touchTimeout),t.touchTimeout=setTimeout(function(e){return t.cycle(e)},500+t._config.interval)}))},e._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},e._getItemIndex=function(t){return this._items=D.makeArray(D(t).parent().find(K.ITEM)),this._items.indexOf(t)},e._getItemByDirection=function(t,e){var n=t===x,i=t===j,r=this._getItemIndex(e),o=this._items.length-1;if((i&&0===r||n&&r===o)&&!this._config.wrap)return e;var s=(r+(t===j?-1:1))%this._items.length;return-1===s?this._items[this._items.length-1]:this._items[s]},e._triggerSlideEvent=function(t,e){var n=this._getItemIndex(t),i=this._getItemIndex(D(this._element).find(K.ACTIVE_ITEM)[0]),r=D.Event(R.SLIDE,{relatedTarget:t,direction:e,from:i,to:n});return D(this._element).trigger(r),r},e._setActiveIndicatorElement=function(t){if(this._indicatorsElement){D(this._indicatorsElement).find(K.ACTIVE).removeClass(F);var e=this._indicatorsElement.children[this._getItemIndex(t)];e&&D(e).addClass(F)}},e._slide=function(t,e){var n,i,r,o=this,s=D(this._element).find(K.ACTIVE_ITEM)[0],a=this._getItemIndex(s),l=e||s&&this._getItemByDirection(t,s),c=this._getItemIndex(l),h=Boolean(this._interval);if(t===x?(n=M,i=U,r="left"):(n=W,i=B,r="right"),l&&D(l).hasClass(F))this._isSliding=!1;else if(!this._triggerSlideEvent(l,r).isDefaultPrevented()&&s&&l){this._isSliding=!0,h&&this.pause(),this._setActiveIndicatorElement(l);var f=D.Event(R.SLID,{relatedTarget:l,direction:r,from:a,to:c});if(D(this._element).hasClass("slide")){D(l).addClass(i),rn.reflow(l),D(s).addClass(n),D(l).addClass(n);var u=rn.getTransitionDurationFromElement(s);D(s).one(rn.TRANSITION_END,function(){D(l).removeClass(n+" "+i).addClass(F),D(s).removeClass(F+" "+i+" "+n),o._isSliding=!1,setTimeout(function(){return D(o._element).trigger(f)},0)}).emulateTransitionEnd(u)}else D(s).removeClass(F),D(l).addClass(F),this._isSliding=!1,D(this._element).trigger(f);h&&this.cycle()}},t._jQueryInterface=function(e){return this.each(function(){var n=D(this).data(S),i=o({},L,D(this).data());"object"==typeof e&&(i=o({},i,e));var r="string"==typeof e?e:i.slide;if(n||(n=new t(this,i),D(this).data(S,n)),"number"==typeof e)n.to(e);else if("string"==typeof r){if(void 0===n[r])throw new TypeError('No method named "'+r+'"');n[r]()}else i.interval&&(n.pause(),n.cycle())})},t._dataApiClickHandler=function(e){var n=rn.getSelectorFromElement(this);if(n){var i=D(n)[0];if(i&&D(i).hasClass(H)){var r=o({},D(i).data(),D(this).data()),s=this.getAttribute("data-slide-to");s&&(r.interval=!1),t._jQueryInterface.call(D(i),r),s&&D(i).data(S).to(s),e.preventDefault()}}},r(t,null,[{key:"VERSION",get:function(){return"4.1.1"}},{key:"Default",get:function(){return L}}]),t}(),D(document).on(R.CLICK_DATA_API,K.DATA_SLIDE,V._dataApiClickHandler),D(window).on(R.LOAD_DATA_API,function(){D(K.DATA_RIDE).each(function(){var t=D(this);V._jQueryInterface.call(t,t.data())})}),D.fn[A]=V._jQueryInterface,D.fn[A].Constructor=V,D.fn[A].noConflict=function(){return D.fn[A]=k,V._jQueryInterface},V),ln=(Y="collapse",q="."+(G="bs.collapse"),z=(Q=e).fn[Y],X={toggle:!0,parent:""},J={toggle:"boolean",parent:"(string|element)"},Z={SHOW:"show"+q,SHOWN:"shown"+q,HIDE:"hide"+q,HIDDEN:"hidden"+q,CLICK_DATA_API:"click"+q+".data-api"},$="show",tt="collapse",et="collapsing",nt="collapsed",it="width","height",rt={ACTIVES:".show, .collapsing",DATA_TOGGLE:'[data-toggle="collapse"]'},ot=function(){function t(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=Q.makeArray(Q('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var n=Q(rt.DATA_TOGGLE),i=0;i<n.length;i++){var r=n[i],o=rn.getSelectorFromElement(r);null!==o&&0<Q(o).filter(t).length&&(this._selector=o,this._triggerArray.push(r))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var e=t.prototype;return e.toggle=function(){Q(this._element).hasClass($)?this.hide():this.show()},e.show=function(){var e,n,i=this;if(!(this._isTransitioning||Q(this._element).hasClass($)||(this._parent&&0===(e=Q.makeArray(Q(this._parent).find(rt.ACTIVES).filter('[data-parent="'+this._config.parent+'"]'))).length&&(e=null),e&&(n=Q(e).not(this._selector).data(G))&&n._isTransitioning))){var r=Q.Event(Z.SHOW);if(Q(this._element).trigger(r),!r.isDefaultPrevented()){e&&(t._jQueryInterface.call(Q(e).not(this._selector),"hide"),n||Q(e).data(G,null));var o=this._getDimension();Q(this._element).removeClass(tt).addClass(et),(this._element.style[o]=0)<this._triggerArray.length&&Q(this._triggerArray).removeClass(nt).attr("aria-expanded",!0),this.setTransitioning(!0);var s="scroll"+(o[0].toUpperCase()+o.slice(1)),a=rn.getTransitionDurationFromElement(this._element);Q(this._element).one(rn.TRANSITION_END,function(){Q(i._element).removeClass(et).addClass(tt).addClass($),i._element.style[o]="",i.setTransitioning(!1),Q(i._element).trigger(Z.SHOWN)}).emulateTransitionEnd(a),this._element.style[o]=this._element[s]+"px"}}},e.hide=function(){var t=this;if(!this._isTransitioning&&Q(this._element).hasClass($)){var e=Q.Event(Z.HIDE);if(Q(this._element).trigger(e),!e.isDefaultPrevented()){var n=this._getDimension();if(this._element.style[n]=this._element.getBoundingClientRect()[n]+"px",rn.reflow(this._element),Q(this._element).addClass(et).removeClass(tt).removeClass($),0<this._triggerArray.length)for(var i=0;i<this._triggerArray.length;i++){var r=this._triggerArray[i],o=rn.getSelectorFromElement(r);null!==o&&(Q(o).hasClass($)||Q(r).addClass(nt).attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[n]="";var s=rn.getTransitionDurationFromElement(this._element);Q(this._element).one(rn.TRANSITION_END,function(){t.setTransitioning(!1),Q(t._element).removeClass(et).addClass(tt).trigger(Z.HIDDEN)}).emulateTransitionEnd(s)}}},e.setTransitioning=function(t){this._isTransitioning=t},e.dispose=function(){Q.removeData(this._element,G),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},e._getConfig=function(t){return(t=o({},X,t)).toggle=Boolean(t.toggle),rn.typeCheckConfig(Y,t,J),t},e._getDimension=function(){return Q(this._element).hasClass(it)?it:"height"},e._getParent=function(){var e=this,n=null;rn.isElement(this._config.parent)?(n=this._config.parent,void 0!==this._config.parent.jquery&&(n=this._config.parent[0])):n=Q(this._config.parent)[0];var i='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]';return Q(n).find(i).each(function(n,i){e._addAriaAndCollapsedClass(t._getTargetFromElement(i),[i])}),n},e._addAriaAndCollapsedClass=function(t,e){if(t){var n=Q(t).hasClass($);0<e.length&&Q(e).toggleClass(nt,!n).attr("aria-expanded",n)}},t._getTargetFromElement=function(t){var e=rn.getSelectorFromElement(t);return e?Q(e)[0]:null},t._jQueryInterface=function(e){return this.each(function(){var n=Q(this),i=n.data(G),r=o({},X,n.data(),"object"==typeof e&&e?e:{});if(!i&&r.toggle&&/show|hide/.test(e)&&(r.toggle=!1),i||(i=new t(this,r),n.data(G,i)),"string"==typeof e){if(void 0===i[e])throw new TypeError('No method named "'+e+'"');i[e]()}})},r(t,null,[{key:"VERSION",get:function(){return"4.1.1"}},{key:"Default",get:function(){return X}}]),t}(),Q(document).on(Z.CLICK_DATA_API,rt.DATA_TOGGLE,function(t){"A"===t.currentTarget.tagName&&t.preventDefault();var e=Q(this),n=rn.getSelectorFromElement(this);Q(n).each(function(){var t=Q(this),n=t.data(G)?"toggle":e.data();ot._jQueryInterface.call(t,n)})}),Q.fn[Y]=ot._jQueryInterface,Q.fn[Y].Constructor=ot,Q.fn[Y].noConflict=function(){return Q.fn[Y]=z,ot._jQueryInterface},ot),cn=(at="dropdown",ct="."+(lt="bs.dropdown"),ht=".data-api",ft=(st=e).fn[at],ut=new RegExp("38|40|27"),dt={HIDE:"hide"+ct,HIDDEN:"hidden"+ct,SHOW:"show"+ct,SHOWN:"shown"+ct,CLICK:"click"+ct,CLICK_DATA_API:"click"+ct+ht,KEYDOWN_DATA_API:"keydown"+ct+ht,KEYUP_DATA_API:"keyup"+ct+ht},pt="disabled",gt="show","dropup",mt="dropright",_t="dropleft",vt="dropdown-menu-right",Et="position-static",yt='[data-toggle="dropdown"]',bt=".dropdown form",Tt=".dropdown-menu",Ct=".navbar-nav",wt=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",It="top-start",Dt="top-end",At="bottom-start",St="bottom-end",Ot="right-start",Nt="left-start",kt={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic"},Lt={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string"},Pt=function(){function t(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var e=t.prototype;return e.toggle=function(){if(!this._element.disabled&&!st(this._element).hasClass(pt)){var e=t._getParentFromElement(this._element),i=st(this._menu).hasClass(gt);if(t._clearMenus(),!i){var r={relatedTarget:this._element},o=st.Event(dt.SHOW,r);if(st(e).trigger(o),!o.isDefaultPrevented()){if(!this._inNavbar){if(void 0===n)throw new TypeError("Bootstrap dropdown require Popper.js (https://popper.js.org)");var s=this._element;"parent"===this._config.reference?s=e:rn.isElement(this._config.reference)&&(s=this._config.reference,void 0!==this._config.reference.jquery&&(s=this._config.reference[0])),"scrollParent"!==this._config.boundary&&st(e).addClass(Et),this._popper=new n(s,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===st(e).closest(Ct).length&&st(document.body).children().on("mouseover",null,st.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),st(this._menu).toggleClass(gt),st(e).toggleClass(gt).trigger(st.Event(dt.SHOWN,r))}}}},e.dispose=function(){st.removeData(this._element,lt),st(this._element).off(ct),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},e.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},e._addEventListeners=function(){var t=this;st(this._element).on(dt.CLICK,function(e){e.preventDefault(),e.stopPropagation(),t.toggle()})},e._getConfig=function(t){return t=o({},this.constructor.Default,st(this._element).data(),t),rn.typeCheckConfig(at,t,this.constructor.DefaultType),t},e._getMenuElement=function(){if(!this._menu){var e=t._getParentFromElement(this._element);this._menu=st(e).find(Tt)[0]}return this._menu},e._getPlacement=function(){var t=st(this._element).parent(),e=At;return t.hasClass("dropup")?(e=It,st(this._menu).hasClass(vt)&&(e=Dt)):t.hasClass(mt)?e=Ot:t.hasClass(_t)?e=Nt:st(this._menu).hasClass(vt)&&(e=St),e},e._detectNavbar=function(){return 0<st(this._element).closest(".navbar").length},e._getPopperConfig=function(){var t=this,e={};"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=o({},e.offsets,t._config.offset(e.offsets)||{}),e}:e.offset=this._config.offset;var n={placement:this._getPlacement(),modifiers:{offset:e,flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(n.modifiers.applyStyle={enabled:!1}),n},t._jQueryInterface=function(e){return this.each(function(){var n=st(this).data(lt);if(n||(n=new t(this,"object"==typeof e?e:null),st(this).data(lt,n)),"string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}})},t._clearMenus=function(e){if(!e||3!==e.which&&("keyup"!==e.type||9===e.which))for(var n=st.makeArray(st(yt)),i=0;i<n.length;i++){var r=t._getParentFromElement(n[i]),o=st(n[i]).data(lt),s={relatedTarget:n[i]};if(o){var a=o._menu;if(st(r).hasClass(gt)&&!(e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&9===e.which)&&st.contains(r,e.target))){var l=st.Event(dt.HIDE,s);st(r).trigger(l),l.isDefaultPrevented()||("ontouchstart"in document.documentElement&&st(document.body).children().off("mouseover",null,st.noop),n[i].setAttribute("aria-expanded","false"),st(a).removeClass(gt),st(r).removeClass(gt).trigger(st.Event(dt.HIDDEN,s)))}}}},t._getParentFromElement=function(t){var e,n=rn.getSelectorFromElement(t);return n&&(e=st(n)[0]),e||t.parentNode},t._dataApiKeydownHandler=function(e){if((/input|textarea/i.test(e.target.tagName)?!(32===e.which||27!==e.which&&(40!==e.which&&38!==e.which||st(e.target).closest(Tt).length)):ut.test(e.which))&&(e.preventDefault(),e.stopPropagation(),!this.disabled&&!st(this).hasClass(pt))){var n=t._getParentFromElement(this),i=st(n).hasClass(gt);if((i||27===e.which&&32===e.which)&&(!i||27!==e.which&&32!==e.which)){var r=st(n).find(wt).get();if(0!==r.length){var o=r.indexOf(e.target);38===e.which&&0<o&&o--,40===e.which&&o<r.length-1&&o++,o<0&&(o=0),r[o].focus()}}else{if(27===e.which){var s=st(n).find(yt)[0];st(s).trigger("focus")}st(this).trigger("click")}}},r(t,null,[{key:"VERSION",get:function(){return"4.1.1"}},{key:"Default",get:function(){return kt}},{key:"DefaultType",get:function(){return Lt}}]),t}(),st(document).on(dt.KEYDOWN_DATA_API,yt,Pt._dataApiKeydownHandler).on(dt.KEYDOWN_DATA_API,Tt,Pt._dataApiKeydownHandler).on(dt.CLICK_DATA_API+" "+dt.KEYUP_DATA_API,Pt._clearMenus).on(dt.CLICK_DATA_API,yt,function(t){t.preventDefault(),t.stopPropagation(),Pt._jQueryInterface.call(st(this),"toggle")}).on(dt.CLICK_DATA_API,bt,function(t){t.stopPropagation()}),st.fn[at]=Pt._jQueryInterface,st.fn[at].Constructor=Pt,st.fn[at].noConflict=function(){return st.fn[at]=ft,Pt._jQueryInterface},Pt),hn=(jt="modal",Ht="."+(Rt="bs.modal"),Ft=(xt=e).fn[jt],Wt={backdrop:!0,keyboard:!0,focus:!0,show:!0},Mt={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},Ut={HIDE:"hide"+Ht,HIDDEN:"hidden"+Ht,SHOW:"show"+Ht,SHOWN:"shown"+Ht,FOCUSIN:"focusin"+Ht,RESIZE:"resize"+Ht,CLICK_DISMISS:"click.dismiss"+Ht,KEYDOWN_DISMISS:"keydown.dismiss"+Ht,MOUSEUP_DISMISS:"mouseup.dismiss"+Ht,MOUSEDOWN_DISMISS:"mousedown.dismiss"+Ht,CLICK_DATA_API:"click"+Ht+".data-api"},Bt="modal-scrollbar-measure",Kt="modal-backdrop",Vt="modal-open",Qt="fade",Yt="show",Gt={DIALOG:".modal-dialog",DATA_TOGGLE:'[data-toggle="modal"]',DATA_DISMISS:'[data-dismiss="modal"]',FIXED_CONTENT:".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",STICKY_CONTENT:".sticky-top",NAVBAR_TOGGLER:".navbar-toggler"},qt=function(){function t(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=xt(t).find(Gt.DIALOG)[0],this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._scrollbarWidth=0}var e=t.prototype;return e.toggle=function(t){return this._isShown?this.hide():this.show(t)},e.show=function(t){var e=this;if(!this._isTransitioning&&!this._isShown){xt(this._element).hasClass(Qt)&&(this._isTransitioning=!0);var n=xt.Event(Ut.SHOW,{relatedTarget:t});xt(this._element).trigger(n),this._isShown||n.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),xt(document.body).addClass(Vt),this._setEscapeEvent(),this._setResizeEvent(),xt(this._element).on(Ut.CLICK_DISMISS,Gt.DATA_DISMISS,function(t){return e.hide(t)}),xt(this._dialog).on(Ut.MOUSEDOWN_DISMISS,function(){xt(e._element).one(Ut.MOUSEUP_DISMISS,function(t){xt(t.target).is(e._element)&&(e._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return e._showElement(t)}))}},e.hide=function(t){var e=this;if(t&&t.preventDefault(),!this._isTransitioning&&this._isShown){var n=xt.Event(Ut.HIDE);if(xt(this._element).trigger(n),this._isShown&&!n.isDefaultPrevented()){this._isShown=!1;var i=xt(this._element).hasClass(Qt);if(i&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),xt(document).off(Ut.FOCUSIN),xt(this._element).removeClass(Yt),xt(this._element).off(Ut.CLICK_DISMISS),xt(this._dialog).off(Ut.MOUSEDOWN_DISMISS),i){var r=rn.getTransitionDurationFromElement(this._element);xt(this._element).one(rn.TRANSITION_END,function(t){return e._hideModal(t)}).emulateTransitionEnd(r)}else this._hideModal()}}},e.dispose=function(){xt.removeData(this._element,Rt),xt(window,document,this._element,this._backdrop).off(Ht),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._scrollbarWidth=null},e.handleUpdate=function(){this._adjustDialog()},e._getConfig=function(t){return t=o({},Wt,t),rn.typeCheckConfig(jt,t,Mt),t},e._showElement=function(t){var e=this,n=xt(this._element).hasClass(Qt);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.scrollTop=0,n&&rn.reflow(this._element),xt(this._element).addClass(Yt),this._config.focus&&this._enforceFocus();var i=xt.Event(Ut.SHOWN,{relatedTarget:t}),r=function(){e._config.focus&&e._element.focus(),e._isTransitioning=!1,xt(e._element).trigger(i)};if(n){var o=rn.getTransitionDurationFromElement(this._element);xt(this._dialog).one(rn.TRANSITION_END,r).emulateTransitionEnd(o)}else r()},e._enforceFocus=function(){var t=this;xt(document).off(Ut.FOCUSIN).on(Ut.FOCUSIN,function(e){document!==e.target&&t._element!==e.target&&0===xt(t._element).has(e.target).length&&t._element.focus()})},e._setEscapeEvent=function(){var t=this;this._isShown&&this._config.keyboard?xt(this._element).on(Ut.KEYDOWN_DISMISS,function(e){27===e.which&&(e.preventDefault(),t.hide())}):this._isShown||xt(this._element).off(Ut.KEYDOWN_DISMISS)},e._setResizeEvent=function(){var t=this;this._isShown?xt(window).on(Ut.RESIZE,function(e){return t.handleUpdate(e)}):xt(window).off(Ut.RESIZE)},e._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._isTransitioning=!1,this._showBackdrop(function(){xt(document.body).removeClass(Vt),t._resetAdjustments(),t._resetScrollbar(),xt(t._element).trigger(Ut.HIDDEN)})},e._removeBackdrop=function(){this._backdrop&&(xt(this._backdrop).remove(),this._backdrop=null)},e._showBackdrop=function(t){var e=this,n=xt(this._element).hasClass(Qt)?Qt:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=Kt,n&&xt(this._backdrop).addClass(n),xt(this._backdrop).appendTo(document.body),xt(this._element).on(Ut.CLICK_DISMISS,function(t){e._ignoreBackdropClick?e._ignoreBackdropClick=!1:t.target===t.currentTarget&&("static"===e._config.backdrop?e._element.focus():e.hide())}),n&&rn.reflow(this._backdrop),xt(this._backdrop).addClass(Yt),!t)return;if(!n)return void t();var i=rn.getTransitionDurationFromElement(this._backdrop);xt(this._backdrop).one(rn.TRANSITION_END,t).emulateTransitionEnd(i)}else if(!this._isShown&&this._backdrop){xt(this._backdrop).removeClass(Yt);var r=function(){e._removeBackdrop(),t&&t()};if(xt(this._element).hasClass(Qt)){var o=rn.getTransitionDurationFromElement(this._backdrop);xt(this._backdrop).one(rn.TRANSITION_END,r).emulateTransitionEnd(o)}else r()}else t&&t()},e._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},e._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},e._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=t.left+t.right<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},e._setScrollbar=function(){var t=this;if(this._isBodyOverflowing){xt(Gt.FIXED_CONTENT).each(function(e,n){var i=xt(n)[0].style.paddingRight,r=xt(n).css("padding-right");xt(n).data("padding-right",i).css("padding-right",parseFloat(r)+t._scrollbarWidth+"px")}),xt(Gt.STICKY_CONTENT).each(function(e,n){var i=xt(n)[0].style.marginRight,r=xt(n).css("margin-right");xt(n).data("margin-right",i).css("margin-right",parseFloat(r)-t._scrollbarWidth+"px")}),xt(Gt.NAVBAR_TOGGLER).each(function(e,n){var i=xt(n)[0].style.marginRight,r=xt(n).css("margin-right");xt(n).data("margin-right",i).css("margin-right",parseFloat(r)+t._scrollbarWidth+"px")});var e=document.body.style.paddingRight,n=xt(document.body).css("padding-right");xt(document.body).data("padding-right",e).css("padding-right",parseFloat(n)+this._scrollbarWidth+"px")}},e._resetScrollbar=function(){xt(Gt.FIXED_CONTENT).each(function(t,e){var n=xt(e).data("padding-right");void 0!==n&&xt(e).css("padding-right",n).removeData("padding-right")}),xt(Gt.STICKY_CONTENT+", "+Gt.NAVBAR_TOGGLER).each(function(t,e){var n=xt(e).data("margin-right");void 0!==n&&xt(e).css("margin-right",n).removeData("margin-right")});var t=xt(document.body).data("padding-right");void 0!==t&&xt(document.body).css("padding-right",t).removeData("padding-right")},e._getScrollbarWidth=function(){var t=document.createElement("div");t.className=Bt,document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},t._jQueryInterface=function(e,n){return this.each(function(){var i=xt(this).data(Rt),r=o({},Wt,xt(this).data(),"object"==typeof e&&e?e:{});if(i||(i=new t(this,r),xt(this).data(Rt,i)),"string"==typeof e){if(void 0===i[e])throw new TypeError('No method named "'+e+'"');i[e](n)}else r.show&&i.show(n)})},r(t,null,[{key:"VERSION",get:function(){return"4.1.1"}},{key:"Default",get:function(){return Wt}}]),t}(),xt(document).on(Ut.CLICK_DATA_API,Gt.DATA_TOGGLE,function(t){var e,n=this,i=rn.getSelectorFromElement(this);i&&(e=xt(i)[0]);var r=xt(e).data(Rt)?"toggle":o({},xt(e).data(),xt(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault();var s=xt(e).one(Ut.SHOW,function(t){t.isDefaultPrevented()||s.one(Ut.HIDDEN,function(){xt(n).is(":visible")&&n.focus()})});qt._jQueryInterface.call(xt(e),r,this)}),xt.fn[jt]=qt._jQueryInterface,xt.fn[jt].Constructor=qt,xt.fn[jt].noConflict=function(){return xt.fn[jt]=Ft,qt._jQueryInterface},qt),fn=(Xt="tooltip",Zt="."+(Jt="bs.tooltip"),$t=(zt=e).fn[Xt],te="bs-tooltip",ee=new RegExp("(^|\\s)"+te+"\\S+","g"),re={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!(ie={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"}),selector:!(ne={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)"}),placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent"},se="out",ae={HIDE:"hide"+Zt,HIDDEN:"hidden"+Zt,SHOW:(oe="show")+Zt,SHOWN:"shown"+Zt,INSERTED:"inserted"+Zt,CLICK:"click"+Zt,FOCUSIN:"focusin"+Zt,FOCUSOUT:"focusout"+Zt,MOUSEENTER:"mouseenter"+Zt,MOUSELEAVE:"mouseleave"+Zt},le="fade",ce="show",he=".tooltip-inner",".arrow",fe="hover",ue="focus","click","manual",de=function(){function t(t,e){if(void 0===n)throw new TypeError("Bootstrap tooltips require Popper.js (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}var e=t.prototype;return e.enable=function(){this._isEnabled=!0},e.disable=function(){this._isEnabled=!1},e.toggleEnabled=function(){this._isEnabled=!this._isEnabled},e.toggle=function(t){if(this._isEnabled)if(t){var e=this.constructor.DATA_KEY,n=zt(t.currentTarget).data(e);n||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),zt(t.currentTarget).data(e,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(zt(this.getTipElement()).hasClass(ce))return void this._leave(null,this);this._enter(null,this)}},e.dispose=function(){clearTimeout(this._timeout),zt.removeData(this.element,this.constructor.DATA_KEY),zt(this.element).off(this.constructor.EVENT_KEY),zt(this.element).closest(".modal").off("hide.bs.modal"),this.tip&&zt(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,(this._activeTrigger=null)!==this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},e.show=function(){var t=this;if("none"===zt(this.element).css("display"))throw new Error("Please use show on visible elements");var e=zt.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){zt(this.element).trigger(e);var i=zt.contains(this.element.ownerDocument.documentElement,this.element);if(e.isDefaultPrevented()||!i)return;var r=this.getTipElement(),o=rn.getUID(this.constructor.NAME);r.setAttribute("id",o),this.element.setAttribute("aria-describedby",o),this.setContent(),this.config.animation&&zt(r).addClass(le);var s="function"==typeof this.config.placement?this.config.placement.call(this,r,this.element):this.config.placement,a=this._getAttachment(s);this.addAttachmentClass(a);var l=!1===this.config.container?document.body:zt(this.config.container);zt(r).data(this.constructor.DATA_KEY,this),zt.contains(this.element.ownerDocument.documentElement,this.tip)||zt(r).appendTo(l),zt(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new n(this.element,r,{placement:a,modifiers:{offset:{offset:this.config.offset},flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){t._handlePopperPlacementChange(e)}}),zt(r).addClass(ce),"ontouchstart"in document.documentElement&&zt(document.body).children().on("mouseover",null,zt.noop);var c=function(){t.config.animation&&t._fixTransition();var e=t._hoverState;t._hoverState=null,zt(t.element).trigger(t.constructor.Event.SHOWN),e===se&&t._leave(null,t)};if(zt(this.tip).hasClass(le)){var h=rn.getTransitionDurationFromElement(this.tip);zt(this.tip).one(rn.TRANSITION_END,c).emulateTransitionEnd(h)}else c()}},e.hide=function(t){var e=this,n=this.getTipElement(),i=zt.Event(this.constructor.Event.HIDE),r=function(){e._hoverState!==oe&&n.parentNode&&n.parentNode.removeChild(n),e._cleanTipClass(),e.element.removeAttribute("aria-describedby"),zt(e.element).trigger(e.constructor.Event.HIDDEN),null!==e._popper&&e._popper.destroy(),t&&t()};if(zt(this.element).trigger(i),!i.isDefaultPrevented()){if(zt(n).removeClass(ce),"ontouchstart"in document.documentElement&&zt(document.body).children().off("mouseover",null,zt.noop),this._activeTrigger.click=!1,this._activeTrigger[ue]=!1,this._activeTrigger[fe]=!1,zt(this.tip).hasClass(le)){var o=rn.getTransitionDurationFromElement(n);zt(n).one(rn.TRANSITION_END,r).emulateTransitionEnd(o)}else r();this._hoverState=""}},e.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},e.isWithContent=function(){return Boolean(this.getTitle())},e.addAttachmentClass=function(t){zt(this.getTipElement()).addClass(te+"-"+t)},e.getTipElement=function(){return this.tip=this.tip||zt(this.config.template)[0],this.tip},e.setContent=function(){var t=zt(this.getTipElement());this.setElementContent(t.find(he),this.getTitle()),t.removeClass(le+" "+ce)},e.setElementContent=function(t,e){var n=this.config.html;"object"==typeof e&&(e.nodeType||e.jquery)?n?zt(e).parent().is(t)||t.empty().append(e):t.text(zt(e).text()):t[n?"html":"text"](e)},e.getTitle=function(){var t=this.element.getAttribute("data-original-title");return t||(t="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),t},e._getAttachment=function(t){return ie[t.toUpperCase()]},e._setListeners=function(){var t=this;this.config.trigger.split(" ").forEach(function(e){if("click"===e)zt(t.element).on(t.constructor.Event.CLICK,t.config.selector,function(e){return t.toggle(e)});else if("manual"!==e){var n=e===fe?t.constructor.Event.MOUSEENTER:t.constructor.Event.FOCUSIN,i=e===fe?t.constructor.Event.MOUSELEAVE:t.constructor.Event.FOCUSOUT;zt(t.element).on(n,t.config.selector,function(e){return t._enter(e)}).on(i,t.config.selector,function(e){return t._leave(e)})}zt(t.element).closest(".modal").on("hide.bs.modal",function(){return t.hide()})}),this.config.selector?this.config=o({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},e._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==t)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},e._enter=function(t,e){var n=this.constructor.DATA_KEY;(e=e||zt(t.currentTarget).data(n))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),zt(t.currentTarget).data(n,e)),t&&(e._activeTrigger["focusin"===t.type?ue:fe]=!0),zt(e.getTipElement()).hasClass(ce)||e._hoverState===oe?e._hoverState=oe:(clearTimeout(e._timeout),e._hoverState=oe,e.config.delay&&e.config.delay.show?e._timeout=setTimeout(function(){e._hoverState===oe&&e.show()},e.config.delay.show):e.show())},e._leave=function(t,e){var n=this.constructor.DATA_KEY;(e=e||zt(t.currentTarget).data(n))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),zt(t.currentTarget).data(n,e)),t&&(e._activeTrigger["focusout"===t.type?ue:fe]=!1),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState=se,e.config.delay&&e.config.delay.hide?e._timeout=setTimeout(function(){e._hoverState===se&&e.hide()},e.config.delay.hide):e.hide())},e._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},e._getConfig=function(t){return"number"==typeof(t=o({},this.constructor.Default,zt(this.element).data(),"object"==typeof t&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),rn.typeCheckConfig(Xt,t,this.constructor.DefaultType),t},e._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},e._cleanTipClass=function(){var t=zt(this.getTipElement()),e=t.attr("class").match(ee);null!==e&&0<e.length&&t.removeClass(e.join(""))},e._handlePopperPlacementChange=function(t){this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},e._fixTransition=function(){var t=this.getTipElement(),e=this.config.animation;null===t.getAttribute("x-placement")&&(zt(t).removeClass(le),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)},t._jQueryInterface=function(e){return this.each(function(){var n=zt(this).data(Jt),i="object"==typeof e&&e;if((n||!/dispose|hide/.test(e))&&(n||(n=new t(this,i),zt(this).data(Jt,n)),"string"==typeof e)){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}})},r(t,null,[{key:"VERSION",get:function(){return"4.1.1"}},{key:"Default",get:function(){return re}},{key:"NAME",get:function(){return Xt}},{key:"DATA_KEY",get:function(){return Jt}},{key:"Event",get:function(){return ae}},{key:"EVENT_KEY",get:function(){return Zt}},{key:"DefaultType",get:function(){return ne}}]),t}(),zt.fn[Xt]=de._jQueryInterface,zt.fn[Xt].Constructor=de,zt.fn[Xt].noConflict=function(){return zt.fn[Xt]=$t,de._jQueryInterface},de),un=(ge="popover",_e="."+(me="bs.popover"),ve=(pe=e).fn[ge],Ee="bs-popover",ye=new RegExp("(^|\\s)"+Ee+"\\S+","g"),be=o({},fn.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),Te=o({},fn.DefaultType,{content:"(string|element|function)"}),"fade",Ce=".popover-header",we=".popover-body",Ie={HIDE:"hide"+_e,HIDDEN:"hidden"+_e,SHOW:"show"+_e,SHOWN:"shown"+_e,INSERTED:"inserted"+_e,CLICK:"click"+_e,FOCUSIN:"focusin"+_e,FOCUSOUT:"focusout"+_e,MOUSEENTER:"mouseenter"+_e,MOUSELEAVE:"mouseleave"+_e},De=function(t){var e,n;function i(){return t.apply(this,arguments)||this}n=t,(e=i).prototype=Object.create(n.prototype),(e.prototype.constructor=e).__proto__=n;var o=i.prototype;return o.isWithContent=function(){return this.getTitle()||this._getContent()},o.addAttachmentClass=function(t){pe(this.getTipElement()).addClass(Ee+"-"+t)},o.getTipElement=function(){return this.tip=this.tip||pe(this.config.template)[0],this.tip},o.setContent=function(){var t=pe(this.getTipElement());this.setElementContent(t.find(Ce),this.getTitle());var e=this._getContent();"function"==typeof e&&(e=e.call(this.element)),this.setElementContent(t.find(we),e),t.removeClass("fade show")},o._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},o._cleanTipClass=function(){var t=pe(this.getTipElement()),e=t.attr("class").match(ye);null!==e&&0<e.length&&t.removeClass(e.join(""))},i._jQueryInterface=function(t){return this.each(function(){var e=pe(this).data(me),n="object"==typeof t?t:null;if((e||!/destroy|hide/.test(t))&&(e||(e=new i(this,n),pe(this).data(me,e)),"string"==typeof t)){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},r(i,null,[{key:"VERSION",get:function(){return"4.1.1"}},{key:"Default",get:function(){return be}},{key:"NAME",get:function(){return ge}},{key:"DATA_KEY",get:function(){return me}},{key:"Event",get:function(){return Ie}},{key:"EVENT_KEY",get:function(){return _e}},{key:"DefaultType",get:function(){return Te}}]),i}(fn),pe.fn[ge]=De._jQueryInterface,pe.fn[ge].Constructor=De,pe.fn[ge].noConflict=function(){return pe.fn[ge]=ve,De._jQueryInterface},De),dn=(Se="scrollspy",Ne="."+(Oe="bs.scrollspy"),ke=(Ae=e).fn[Se],Le={offset:10,method:"auto",target:""},Pe={offset:"number",method:"string",target:"(string|element)"},xe={ACTIVATE:"activate"+Ne,SCROLL:"scroll"+Ne,LOAD_DATA_API:"load"+Ne+".data-api"},je="dropdown-item",Re="active",He={DATA_SPY:'[data-spy="scroll"]',ACTIVE:".active",NAV_LIST_GROUP:".nav, .list-group",NAV_LINKS:".nav-link",NAV_ITEMS:".nav-item",LIST_ITEMS:".list-group-item",DROPDOWN:".dropdown",DROPDOWN_ITEMS:".dropdown-item",DROPDOWN_TOGGLE:".dropdown-toggle"},"offset",Fe="position",We=function(){function t(t,e){var n=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(e),this._selector=this._config.target+" "+He.NAV_LINKS+","+this._config.target+" "+He.LIST_ITEMS+","+this._config.target+" "+He.DROPDOWN_ITEMS,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,Ae(this._scrollElement).on(xe.SCROLL,function(t){return n._process(t)}),this.refresh(),this._process()}var e=t.prototype;return e.refresh=function(){var t=this,e=this._scrollElement===this._scrollElement.window?"offset":Fe,n="auto"===this._config.method?e:this._config.method,i=n===Fe?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),Ae.makeArray(Ae(this._selector)).map(function(t){var e,r=rn.getSelectorFromElement(t);if(r&&(e=Ae(r)[0]),e){var o=e.getBoundingClientRect();if(o.width||o.height)return[Ae(e)[n]().top+i,r]}return null}).filter(function(t){return t}).sort(function(t,e){return t[0]-e[0]}).forEach(function(e){t._offsets.push(e[0]),t._targets.push(e[1])})},e.dispose=function(){Ae.removeData(this._element,Oe),Ae(this._scrollElement).off(Ne),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},e._getConfig=function(t){if("string"!=typeof(t=o({},Le,"object"==typeof t&&t?t:{})).target){var e=Ae(t.target).attr("id");e||(e=rn.getUID(Se),Ae(t.target).attr("id",e)),t.target="#"+e}return rn.typeCheckConfig(Se,t,Pe),t},e._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},e._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},e._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},e._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),n=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),n<=t){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var r=this._offsets.length;r--;)this._activeTarget!==this._targets[r]&&t>=this._offsets[r]&&(void 0===this._offsets[r+1]||t<this._offsets[r+1])&&this._activate(this._targets[r])}},e._activate=function(t){this._activeTarget=t,this._clear();var e=this._selector.split(",");e=e.map(function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'});var n=Ae(e.join(","));n.hasClass(je)?(n.closest(He.DROPDOWN).find(He.DROPDOWN_TOGGLE).addClass(Re),n.addClass(Re)):(n.addClass(Re),n.parents(He.NAV_LIST_GROUP).prev(He.NAV_LINKS+", "+He.LIST_ITEMS).addClass(Re),n.parents(He.NAV_LIST_GROUP).prev(He.NAV_ITEMS).children(He.NAV_LINKS).addClass(Re)),Ae(this._scrollElement).trigger(xe.ACTIVATE,{relatedTarget:t})},e._clear=function(){Ae(this._selector).filter(He.ACTIVE).removeClass(Re)},t._jQueryInterface=function(e){return this.each(function(){var n=Ae(this).data(Oe);if(n||(n=new t(this,"object"==typeof e&&e),Ae(this).data(Oe,n)),"string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}})},r(t,null,[{key:"VERSION",get:function(){return"4.1.1"}},{key:"Default",get:function(){return Le}}]),t}(),Ae(window).on(xe.LOAD_DATA_API,function(){for(var t=Ae.makeArray(Ae(He.DATA_SPY)),e=t.length;e--;){var n=Ae(t[e]);We._jQueryInterface.call(n,n.data())}}),Ae.fn[Se]=We._jQueryInterface,Ae.fn[Se].Constructor=We,Ae.fn[Se].noConflict=function(){return Ae.fn[Se]=ke,We._jQueryInterface},We),pn=(Be="."+(Ue="bs.tab"),Ke=(Me=e).fn.tab,Ve={HIDE:"hide"+Be,HIDDEN:"hidden"+Be,SHOW:"show"+Be,SHOWN:"shown"+Be,CLICK_DATA_API:"click"+Be+".data-api"},Qe="dropdown-menu",Ye="active",Ge="disabled","fade",qe="show",ze=".dropdown",Xe=".nav, .list-group",Je=".active",Ze="> li > .active",$e='[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',tn=".dropdown-toggle",en="> .dropdown-menu .active",nn=function(){function t(t){this._element=t}var e=t.prototype;return e.show=function(){var t=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&Me(this._element).hasClass(Ye)||Me(this._element).hasClass(Ge))){var e,n,i=Me(this._element).closest(Xe)[0],r=rn.getSelectorFromElement(this._element);if(i){var o="UL"===i.nodeName?Ze:Je;n=(n=Me.makeArray(Me(i).find(o)))[n.length-1]}var s=Me.Event(Ve.HIDE,{relatedTarget:this._element}),a=Me.Event(Ve.SHOW,{relatedTarget:n});if(n&&Me(n).trigger(s),Me(this._element).trigger(a),!a.isDefaultPrevented()&&!s.isDefaultPrevented()){r&&(e=Me(r)[0]),this._activate(this._element,i);var l=function(){var e=Me.Event(Ve.HIDDEN,{relatedTarget:t._element}),i=Me.Event(Ve.SHOWN,{relatedTarget:n});Me(n).trigger(e),Me(t._element).trigger(i)};e?this._activate(e,e.parentNode,l):l()}}},e.dispose=function(){Me.removeData(this._element,Ue),this._element=null},e._activate=function(t,e,n){var i=this,r=("UL"===e.nodeName?Me(e).find(Ze):Me(e).children(Je))[0],o=n&&r&&Me(r).hasClass("fade"),s=function(){return i._transitionComplete(t,r,n)};if(r&&o){var a=rn.getTransitionDurationFromElement(r);Me(r).one(rn.TRANSITION_END,s).emulateTransitionEnd(a)}else s()},e._transitionComplete=function(t,e,n){if(e){Me(e).removeClass(qe+" "+Ye);var i=Me(e.parentNode).find(en)[0];i&&Me(i).removeClass(Ye),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)}if(Me(t).addClass(Ye),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),rn.reflow(t),Me(t).addClass(qe),t.parentNode&&Me(t.parentNode).hasClass(Qe)){var r=Me(t).closest(ze)[0];r&&Me(r).find(tn).addClass(Ye),t.setAttribute("aria-expanded",!0)}n&&n()},t._jQueryInterface=function(e){return this.each(function(){var n=Me(this),i=n.data(Ue);if(i||(i=new t(this),n.data(Ue,i)),"string"==typeof e){if(void 0===i[e])throw new TypeError('No method named "'+e+'"');i[e]()}})},r(t,null,[{key:"VERSION",get:function(){return"4.1.1"}}]),t}(),Me(document).on(Ve.CLICK_DATA_API,$e,function(t){t.preventDefault(),nn._jQueryInterface.call(Me(this),"show")}),Me.fn.tab=nn._jQueryInterface,Me.fn.tab.Constructor=nn,Me.fn.tab.noConflict=function(){return Me.fn.tab=Ke,nn._jQueryInterface},nn);!function(t){if(void 0===t)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=t.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||4<=e[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}(e),t.Util=rn,t.Alert=on,t.Button=sn,t.Carousel=an,t.Collapse=ln,t.Dropdown=cn,t.Modal=hn,t.Popover=un,t.Scrollspy=dn,t.Tab=pn,t.Tooltip=fn,Object.defineProperty(t,"__esModule",{value:!0})});