/*!------------------------------------------------------------------
[MAIN STYLESHEET]

PROJECT:	Project Name
VERSION:	Versoin Number
-------------------------------------------------------------------*/
/*------------------------------------------------------------------
[TABLE OF CONTENTS]
-------------------------------------------------------------------*/
/*  typography */
@import url("https://fonts.googleapis.com/css2?family=Archivo:wght@400;600&family=Open+Sans:wght@400;600&display=swap");

body {
  line-height: 1.5;
  font-family: "Open Sans", sans-serif;
  -webkit-font-smoothing: antialiased;
  font-size: 15px;
  color: #585757;
}

p,
.paragraph {
  font-weight: 400;
  font-size: 15px;
  line-height: 1.5;
  font-family: "Open Sans", sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #000000;
  font-family: "Archivo", serif;
  font-weight: 600;
  line-height: 1.2;
}

h1,
.h1 {
  font-size: 80px;
}

@media (max-width: 767px) {
  h1,
  .h1 {
    font-size: 55px;
  }
}

h2,
.h2 {
  font-size: 50px;
}

@media (max-width: 767px) {
  h2,
  .h2 {
    font-size: 45px;
  }
}

h3,
.h3 {
  font-size: 40px;
}

h4,
.h4 {
  font-size: 30px;
}

h5,
.h5 {
  font-size: 20px;
}

h6,
.h6 {
  font-size: 16px;
}

/* Button style */
.btn {
  font-size: 15px;
  font-family: "Open Sans", sans-serif;
  text-transform: capitalize;
  padding: 8px 20px;
  border-radius: 0;
  font-weight: 600;
  border: 1px solid;
  position: relative;
  z-index: 1;
  transition: 0.2s ease;
}

.btn:focus {
  outline: 0;
  box-shadow: none !important;
}

.btn:active {
  box-shadow: none;
}

.btn-primary {
  background: #ff6f00;
  color: #fff;
  border: 0;
}

.btn-primary:active {
  background: #ff6f00 !important;
}

.btn-primary:hover {
  background: #ff8c33;
}

.btn-outline-light {
  background: transparent;
  color: #fff;
}

.btn-outline-light:active {
  background: #fff !important;
  color: #000000;
  border-color: #fff;
}

.btn-outline-light:hover {
  background: #fff !important;
  color: #000000;
  border-color: #fff;
}

.btn-transparent {
  color: #ff6f00;
  border: 0;
}

body {
  background-color: #fff;
  overflow-x: hidden;
}

::-moz-selection {
  background: #ff8c33;
  color: #fff;
}

::selection {
  background: #ff8c33;
  color: #fff;
}

/* preloader */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preloader .loader {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 142px;
  height: 40px;
  margin: -20px 0 0 -71px;
  background: white;
}

.preloader .loader .dot {
  position: absolute;
  width: 16px;
  height: 16px;
  top: 12px;
  left: 15px;
  background: #ff6f00;
  border-radius: 50%;
  -webkit-transform: translateX(0);
  transform: translateX(0);
  -webkit-animation: dot 2.8s infinite;
  animation: dot 2.8s infinite;
}

.preloader .loader .dots {
  -webkit-transform: translateX(0);
  transform: translateX(0);
  margin-top: 12px;
  margin-left: 31px;
  -webkit-animation: dots 2.8s infinite;
  animation: dots 2.8s infinite;
}

.preloader .loader .dots span {
  display: block;
  float: left;
  width: 16px;
  height: 16px;
  margin-left: 16px;
  background: #ff6f00;
  border-radius: 50%;
}

@-webkit-keyframes dot {
  50% {
    -webkit-transform: translateX(96px);
    transform: translateX(96px);
  }
}

@keyframes dot {
  50% {
    -webkit-transform: translateX(96px);
    transform: translateX(96px);
  }
}

@-webkit-keyframes dots {
  50% {
    -webkit-transform: translateX(-31px);
    transform: translateX(-31px);
  }
}

@keyframes dots {
  50% {
    -webkit-transform: translateX(-31px);
    transform: translateX(-31px);
  }
}

ol,
ul {
  list-style-type: none;
  margin: 0px;
}

img {
  vertical-align: middle;
  border: 0;
}

a,
a:hover,
a:focus {
  text-decoration: none;
}

a,
button,
select {
  cursor: pointer;
  transition: 0.2s ease;
}

a:focus,
button:focus,
select:focus {
  outline: 0;
}

a:hover {
  color: #ff6f00;
}
a.text-dark:hover {
  color: #ff6f00 !important;
}

.slick-slide {
  outline: 0;
}

.section {
  padding-top: 90px;
  padding-bottom: 90px;
}

.section-sm {
  padding-top: 80px;
  padding-bottom: 80px;
}

.section-title {
  margin-bottom: 30px;
}

.bg-cover {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

.bg-fixed {
  background-attachment: fixed;
  background-size: cover;
  background-position: center top;
  background-repeat: no-repeat;
}

.border-primary {
  border-color: #ababab !important;
}

/* overlay */
.overlay {
  position: relative;
}

.overlay::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.5;
}

.outline-0 {
  outline: 0 !important;
}

.d-unset {
  display: unset !important;
}

.bg-primary {
  background: #ff6f00 !important;
}

.bg-secondary {
  background: #fdefe6 !important;
}

.text-primary {
  color: #ff6f00 !important;
}

.text-color {
  color: #585757;
}

.text-dark {
  color: #000000 !important;
}

.font-secondary {
  font-family: "Archivo", serif !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.mb-60 {
  margin-bottom: 60px !important;
}

.mb-70 {
  margin-bottom: 70px !important;
}

.mb-80 {
  margin-bottom: 80px !important;
}

.mb-90 {
  margin-bottom: 90px !important;
}

.mb-100 {
  margin-bottom: 100px !important;
}

.zindex-1 {
  z-index: 1;
}

.overflow-hidden {
  overflow: hidden;
}

.form-control {
  border: 0;
  border-bottom: 1px solid #ababab;
  border-radius: 0;
}

.form-control:focus {
  outline: 0;
  box-shadow: none;
  border-color: #ff6f00;
}

textarea.form-control {
  height: 150px;
}

.navigation {
  z-index: 9;
}

.headroom {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  transition: all 0.4s ease-in-out;
  padding: 10px 40px;
  background-color: #fff;
}

@media (max-width: 575px) {
  .headroom {
    padding: 10px;
  }
}

.headroom--unpinned {
  top: -150px;
}

@media (max-width: 991px) {
  .headroom--unpinned {
    top: 0;
  }
}

.headroom--pinned {
  top: 0;
}

.navbar .nav-item .nav-link {
  padding: 20px 15px;
}

@media (max-width: 991px) {
  .navbar .nav-item .nav-link {
    padding: 10px;
  }
}

.navbar .dropdown:hover .dropdown-menu {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateY(0);
  transform: translateY(0);
}

.navbar .dropdown-menu {
  box-shadow: 0px 3px 9px 0px rgba(0, 0, 0, 0.12);
  padding: 10px;
  border: 0;
  top: 100%;
  left: -10px;
  border-radius: 0;
  display: block;
  visibility: hidden;
  transition: 0.3s ease;
  opacity: 0;
  -webkit-transform: translateY(-20px);
  transform: translateY(-20px);
  background: #fff;
}

@media (max-width: 991px) {
  .navbar .dropdown-menu {
    display: none;
    opacity: 1;
    visibility: visible;
    -webkit-transform: translateY(0);
    transform: translateY(0);
    text-align: center;
  }
}

.navbar .dropdown-menu.show {
  visibility: hidden;
}

@media (max-width: 991px) {
  .navbar .dropdown-menu.show {
    visibility: visible;
    display: block;
  }
}

.navbar .dropdown-item:active {
  color: #fff;
  background-color: #ff6f00;
}

.search-icon {
  border: 0;
  background: transparent;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.card-img-overlay::before {
  position: absolute;
  content: "";
  background: linear-gradient(transparent 40%, rgba(0, 0, 0, 0.5));
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
}

.card-content {
  padding: 30px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 30px;
}

.title-border {
  position: relative;
  margin-bottom: 25px;
  padding-bottom: 30px;
}

.title-border::before {
  position: absolute;
  content: "";
  height: 2px;
  width: 100px;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  bottom: 0px;
  background: #ababab;
}

.title-border-lg {
  position: relative;
}

.title-border-lg::before {
  position: absolute;
  content: "";
  height: 2px;
  width: 300px;
  left: -45px;
  bottom: -50px;
  background: #ababab;
}

@media (max-width: 575px) {
  .pagination {
    overflow: auto;
    white-space: nowrap;
  }
  .pagination::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }
}

.pagination .page-item .page-link {
  font-family: "Archivo", serif;
  font-size: 30px;
  border: 0;
  color: #585757;
}

@media (max-width: 767px) {
  .pagination .page-item .page-link {
    font-size: 16px;
  }
}

.pagination .page-item .page-link:hover {
  color: #000000;
  background-color: transparent;
}

.pagination .page-item:first-child .page-link,
.pagination .page-item:last-child .page-link {
  font-size: 16px;
}

.pagination .page-item.active .page-link {
  color: #000000;
  text-decoration: underline;
  background: transparent;
}

.instagram-post {
  position: relative;
}

.instagram-post::before {
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: 0.2s ease;
}

.instagram-post ul {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  opacity: 0;
  visibility: hidden;
  transition: 0.2s ease;
}

.instagram-post ul a {
  transition: 0.2s ease;
  font-size: 20px;
}

.instagram-post ul a:hover {
  color: #ff6f00 !important;
}

.instagram-post:hover::before {
  visibility: visible;
  opacity: 1;
}

.instagram-post:hover ul {
  visibility: visible;
  opacity: 1;
}

/* homepage 2 */
.hero-section {
  padding: 70px 0 0;
  background-image: linear-gradient(#fff 45%, #fdefe6 0);
}

.dividers {
  display: flex;
  align-items: center;
  width: 1200px;
  position: fixed;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

.dividers .divider {
  border-right: 1px solid #edeef0;
  flex-basis: 50%;
  height: 100vh;
  opacity: 0.6;
}

.dividers .divider:first-child {
  border-left: 1px solid #edeef0;
}

.article-full-width {
  display: flex;
  flex-wrap: nowrap;
}

@media (max-width: 767px) {
  .article-full-width {
    flex-direction: column;
  }
}

.article-full-width .post-image {
  margin-right: 20px;
}

@media (max-width: 767px) {
  .article-full-width .post-image {
    margin: 0 0 20px 0;
  }
}

.article-full-width .post-image img {
  height: 400px;
  width: auto;
}

@media (max-width: 767px) {
  .article-full-width .post-image img {
    height: auto;
    width: 100%;
  }
}

.article-full-width .post-content {
  width: 50%;
}

@media (max-width: 767px) {
  .article-full-width .post-content {
    width: 100%;
  }
}

.article-full-width .post-content .btn {
  padding-left: 0;
}

.article-full-width .post-meta {
  width: 250px;
}

@media (max-width: 991px) {
  .article-full-width .post-summary {
    display: none;
  }
}

@media (max-width: 767px) {
  .article-full-width .post-summary {
    display: block;
  }
}

.article-full-width.article-right {
  justify-content: flex-end;
}

@media (max-width: 767px) {
  .article-full-width.article-right {
    flex-direction: column-reverse;
  }
}

.article-full-width.article-right .post-image {
  order: 2;
  margin: 0 0 0 20px;
}

@media (max-width: 767px) {
  .article-full-width.article-right .post-image {
    margin: 0 0 20px 0;
  }
}

.article-full-width.article-right .post-content {
  order: 1;
}

.article-full-width.article-right .post-meta {
  margin-left: auto;
}

@media (max-width: 767px) {
  .article-full-width.article-right .post-meta {
    margin-left: 0;
  }
}

.article-full-width.article-right .author {
  text-align: right;
}

@media (max-width: 767px) {
  .article-full-width.article-right .author {
    text-align: left;
  }
}

.post-thumb {
  height: 400px;
}

blockquote {
  font-style: italic;
  color: #000000;
  background: #fdefe6;
  padding: 20px;
  font-weight: 600;
}

.content * {
  margin-bottom: 20px;
}

.content img {
  max-width: 100%;
  height: auto;
}

.widget {
  padding: 15px 0;
}

.widget:not(:last-child) {
  margin-bottom: 30px;
  border-bottom: 1px solid #f4f4f4;
}

.search-box {
  position: relative;
}

.search-box i {
  position: absolute;
  left: 0;
  top: 25px;
  color: #767575;
}

.post-thumb-sm {
  max-width: 75px;
  max-height: 75px;
  overflow: hidden;
}

.tag-list li a {
  display: block;
  background: #f4f4f4;
  padding: 2px 5px;
  color: #000000;
}

.tag-list li a:hover {
  color: #ff6f00;
}
.instagram-slider {
  height: 200px;
  overflow: hidden;
}

.instagram-post img {
  height: 200px;
  width: 200px;
  object-fit: cover;
}
