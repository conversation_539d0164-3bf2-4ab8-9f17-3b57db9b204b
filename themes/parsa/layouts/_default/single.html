{{ define "main" }}

<!-- checking blog -->
{{ if or (eq .Section "post") (eq .Section "posts") (eq .Section "blog") (eq .Section "blogs") (eq .Section "news") (eq .Section "categories") (eq .Section "tags") }}

<!-- page-title -->
<section class="section bg-secondary">
  <div class="container">
    <div class="row">
      <div class="col-lg-12">
        <h4>{{ .Title }}</h4>
      </div>
    </div>
  </div>
</section>
<!-- /page-title -->

<!-- blog single -->
<section>
  <div class="container">
    <div class="row">
      <div class="col-lg-8">
        <ul class="list-inline d-flex justify-content-between py-3">
          <li class="list-inline-item"><i class="ti-user mr-2"></i>Post by {{site.Params.author}}</li>
          <li class="list-inline-item"><i class="ti-calendar mr-2"></i>{{ .PublishDate.Format "Jan 02, 2006" }}</li>
        </ul>
        <article class="content">
          {{ partial "image.html" (dict "Src" .Params.Image "Alt" "post-thumb" "Class" "img-fluid rounded float-left mr-5 mb-4" ) }}
          {{.Content}}
        </article>
      </div>
      {{partial "sidebar.html" . }}
    </div>
  </div>
</section>
<!-- /blog single -->

<!-- regular page -->
{{ else }}
<section class="section">
  <div class="container">
    <div class="row">
      <div class="col-lg-12">
        <h2 class="mb-4">{{.Title }}</h2>
        {{with .Params.image}}
        {{ partial "image.html" (dict "Src" . "Alt" "image" "Class" "img-fluid w-100 mb-4" ) }}
        {{end}}
        <div class="content">{{ .Content }}</div>
      </div>
    </div>
  </div>
</section>
{{ end }}
<!-- /regular page -->

{{ end }}