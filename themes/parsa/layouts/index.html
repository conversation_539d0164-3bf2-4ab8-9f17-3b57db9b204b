{{ define "main" }}

{{ if eq site.Params.layout "1" }}
<!-- featured post -->
<section class="overflow-hidden">
  <div class="container-fluid p-sm-0">
    <div class="row featured-post-slider">
      {{ range (where site.RegularPages "Type" "featured")}}
      <div class="col-lg-3 col-sm-6 mb-2 mb-lg-0 px-1">
        <article class="card bg-dark text-center text-white border-0 rounded-0">
          {{ partial "image.html" (dict "Src" .Params.Image "Alt" "post-thumb" "Class" "card-img rounded-0 img-fluid w-100" ) }}
          <div class="card-img-overlay">
            <div class="card-content">
              <p class="text-uppercase">{{range .Params.Categories}}{{. | humanize}}{{end}}</p>
              <h4 class="card-title mb-4"><a class="text-white" href="{{.Permalink}}">{{.Title}}</a></h4>
              <a class="btn btn-outline-light" href="{{.Permalink}}">read more</a>
            </div>
          </div>
        </article>
      </div>
      {{ end }}
    </div>
  </div>
</section>
<!-- /featured post -->

{{ else if (eq site.Params.layout "2")}}

<!-- hero area -->
<section class="hero-section">
  <div class="container">
    <div class="row">
      <div class="col-lg-6 align-self-end">
        <h1 class="mb-0">Welcome</h1>
        <h2 class="mb-100 title-border-lg">to <i>{{site.Params.author}} Blog</i></h2>
        <p class="mb-80 mr-5">{{site.Params.bio | markdownify }}</p>
        <span class="font-secondary text-dark mr-3 mr-sm-5">Follow me :</span>
        <ul class="list-inline d-inline-block mb-5">
          {{ range site.Params.social }}
          <li class="list-inline-item mx-3"><a href="{{ .link | safeURL }}" class="text-dark"><i class="{{.icon}}"></i></a></li>
          {{ end }}
        </ul>
      </div>
      <div class="col-lg-6 text-right">
        {{ partial "image.html" (dict "Src" site.Params.author_image "Alt" "banner-image" "Class" "img-fluid" ) }}
      </div>
    </div>
  </div>
</section>
<!-- /hero area -->

{{ else }}
<section class="section">
  <div class="container text-center">
    <h1>Please Enter A Valid Homepage Layout Number <br> (1 or 2)</h1>
  </div>
</section>
{{ end }}

<!-- blog post -->
<section class="section">
  <div class="container">
    {{ $paginator := .Paginate (where site.RegularPages "Type" "post") }}
    {{ if eq site.Params.layout "1" }}
    <div class="row masonry-container">
      {{ range $paginator.Pages }}
      <div class="col-lg-4 col-sm-6 mb-5">
        <article class="text-center">
          {{ partial "image.html" (dict "Src" .Params.Image "Alt" "post-thumb" "Class" "img-fluid mb-4" ) }}
          <p class="text-uppercase mb-2">{{range .Params.Categories}}{{. | humanize}}{{end}}</p>
          <h4 class="title-border"><a class="text-dark" href="{{.Permalink}}">{{.Title}}</a></h4>
          <p>{{.Summary}}</p>
          <a href="{{.Permalink}}" class="btn btn-transparent">read more</a>
        </article>
      </div>
      {{ end }}
    </div>

    {{ else if (eq site.Params.layout "2")}}

    <div class="row">
      {{ range $i,$p := (where $paginator.Pages "Type" "post") }}
      <div class="col-12 mb-100">
        <article data-file="{{.Permalink}}" data-target="article"
          class="article-full-width {{if not (modBool $i 2)}} article-right {{ end }}">
          <div class="post-image">
            {{ partial "image.html" (dict "Src" .Params.Image "Alt" "post-thumb" "Class" "img-fluid" ) }}
          </div>
          <div class="post-content">
            <ul class="list-inline d-flex justify-content-between border-bottom post-meta pb-2 mb-4">
              <li class="list-inline-item"><i class="ti-calendar mr-2"></i>{{ .PublishDate.Format "Jan 02, 2006" }}</li>
              <li class="list-inline-item"><i class="ti-alarm-clock mr-2"></i><span class="eta"></span> read</li>
            </ul>
            <h4 class="mb-4"><a href="{{.Permalink}}" class="text-dark">{{ .Title }}</a></h4>
            <p class="mb-0 post-summary">{{.Summary }}</p>
            <a class="btn btn-transparent mb-4" href="{{.Permalink}}">Continue...</a>
          </div>
        </article>
      </div>
      {{ end }}
    </div>
    {{ end }}

    <!-- pagination -->
    <div class="row">
      <div class="col-12">
        {{ $paginator := .Paginator }}
        <!-- Number of links either side of the current page. -->
        {{ $adjacent_links := 2 }}
        <!-- $max_links = ($adjacent_links * 2) + 1 -->
        {{ $max_links := (add (mul $adjacent_links 2) 1) }}
        <!-- $lower_limit = $adjacent_links + 1 -->
        {{ $lower_limit := (add $adjacent_links 1) }}
        <!-- $upper_limit = $paginator.TotalPages - $adjacent_links -->
        {{ $upper_limit := (sub $paginator.TotalPages $adjacent_links) }}
        <!-- If there's more than one page. -->
        {{ if gt $paginator.TotalPages 1 }}
        <nav aria-label="Page navigation">
          <ul class="pagination justify-content-center align-items-center">
            <!-- Previous page. -->
            {{ if $paginator.HasPrev }}
            <li class="page-item">
              <a href="{{ $paginator.Prev.URL }}" class="page-link">
                &laquo; Previous
              </a>
            </li>
            {{ end }}
            <!-- Page numbers. -->
            {{ range $paginator.Pagers }}
            {{ $.Scratch.Set "page_number_flag" false }}
            <!-- Advanced page numbers. -->
            {{ if gt $paginator.TotalPages $max_links }}
            <!-- Lower limit pages. -->
            <!-- If the user is on a page which is in the lower limit.  -->
            {{ if le $paginator.PageNumber $lower_limit }}
            <!-- If the current loop page is less than max_links. -->
            {{ if le .PageNumber $max_links }}
            {{ $.Scratch.Set "page_number_flag" true }}
            {{ end }}
            <!-- Upper limit pages. -->
            <!-- If the user is on a page which is in the upper limit. -->
            {{ else if ge $paginator.PageNumber $upper_limit }}
            <!-- If the current loop page is greater than total pages minus $max_links -->
            {{ if gt .PageNumber (sub $paginator.TotalPages $max_links) }}
            {{ $.Scratch.Set "page_number_flag" true }}
            {{ end }}
            <!-- Middle pages. -->
            {{ else }}
            {{ if and ( ge .PageNumber (sub $paginator.PageNumber $adjacent_links) ) ( le .PageNumber (add $paginator.PageNumber $adjacent_links) ) }}
            {{ $.Scratch.Set "page_number_flag" true }}
            {{ end }}
            {{ end }}
            <!-- Simple page numbers. -->
            {{ else }}
            {{ $.Scratch.Set "page_number_flag" true }}
            {{ end }}
            <!-- Output page numbers. -->
            {{ if eq ($.Scratch.Get "page_number_flag") true }}
            <li class="page-item{{ if eq . $paginator }} page-item active {{ end }}">
              <a href="{{ .URL }}" class="page-link">
                {{ .PageNumber }}
              </a>
            </li>
            {{ end }}
            {{ end }}
            <!-- Next page. -->
            {{ if $paginator.HasNext }}
            <li class="page-item">
              <a href="{{ $paginator.Next.URL }}" class="page-link">
                Next &raquo;
              </a>
            </li>
            {{ end }}
          </ul>
        </nav>
        {{ end }}
      </div>
    </div>
    <!-- pagination -->
  </div>
</section>
<!-- /blog post -->

{{partial "instafeed.html" . }}

{{ end }}