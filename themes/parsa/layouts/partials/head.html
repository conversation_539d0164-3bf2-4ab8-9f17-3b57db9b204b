<head>
  <meta charset="utf-8">
  <title>{{ with .Title }}{{ . }}{{ else }}{{ with site.Title }}{{ . }}{{ end }}{{ end }}</title>

  {{ "<!-- mobile responsive meta -->" | safeHTML }}
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="description" content="{{ with .Description }}{{ . }}{{ else }}{{ with site.Params.description }}{{ . }}{{ end }}{{ end }}">
  {{ with site.Params.author }}<meta name="author" content="{{ . }}">{{ end }}
  <!-- theme meta -->
  <meta name="theme-name" content="parsa-hugo" />
  {{ hugo.Generator }}

  {{ "<!-- plugins -->" | safeHTML }}
  {{ range site.Params.plugins.css }}
  <link rel="stylesheet" href="{{ .link | absURL }} ">
  {{ end }}

  {{ "<!-- Main Stylesheet -->" | safeHTML }}
  {{ $styles := resources.Get "css/style.css" | minify }}
  <link rel="stylesheet" href="{{ $styles.Permalink }}" media="screen">

  {{ "<!--Favicon-->" | safeHTML }}
  <link rel="shortcut icon" href="{{ `images/favicon.png` | absURL }} " type="image/x-icon">
  <link rel="icon" href="{{ `images/favicon.png` | absURL }} " type="image/x-icon">
  
  {{ with .Params.image }}
  <meta property="og:image" content="{{ . | absURL }}" />
  {{ end }}
  {{ template "_internal/opengraph.html" . }}
  {{ template "_internal/google_analytics.html" . }}

</head>
