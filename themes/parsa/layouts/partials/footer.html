<footer class="bg-secondary">
  <div class="section">
    <div class="container">
      <div class="row">
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
          <a href="{{site.BaseURL}}">
            {{ partial "image.html" (dict "Src" site.Params.logo "Alt" "Image" "Class" "img-fluid" ) }}
          </a>
        </div>
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
          {{ if .Site.Params.address }}
          <h6>Address</h6>
          <ul class="list-unstyled">
            <li class="font-secondary text-dark">{{site.Params.address | markdownify }}</li>
          </ul>
          {{ end }}
        </div>
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
          {{ if or  .Site.Params.mobile  .Site.Params.email }}         
          <h6>Contact Info</h6>
          <ul class="list-unstyled">
            {{ if .Site.Params.mobile }}
            <li class="font-secondary text-dark">Tel : {{ .Site.Params.mobile | markdownify }}</li>
            {{ end }}
            {{ if .Site.Params.email }}           
            <li class="font-secondary text-dark">Email : {{ .Site.Params.email | markdownify }}</li>
            {{ end }}
          </ul>
          {{ end }}
        </div>
        <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
          <h6>Follow</h6>
          <ul class="list-inline d-inline-block">
            {{ range site.Params.social }}
            <li class="list-inline-item"><a href="{{ .link | safeURL }}" class="text-dark"><i class="{{ .icon }}"></i></a></li>
            {{ end }}
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="text-center pb-3">
    <p class="mb-0">{{ site.Params.copyright | markdownify }}</p>
  </div>
</footer>


{{ "<!-- JS Plugins -->" | safeHTML }}
{{ range site.Params.plugins.js}}
<script src="{{ .link | absURL }}"></script>
{{ end }}

{{ "<!-- Main Script -->" | safeHTML }}
{{ $script := resources.Get "js/script.js" | minify}}
<script src="{{ $script.Permalink }}"></script>


<!-- cookie -->
{{ if site.Params.cookies.enable }}
<script src="https://cdnjs.cloudflare.com/ajax/libs/js-cookie/2.2.1/js.cookie.min.js"></script>
<div id="js-cookie-box" class="cookie-box cookie-box-hide">
	This site uses cookies. By continuing to use this website, you agree to their use. <span id="js-cookie-button" class="btn btn-sm btn-outline-light ml-2">I Accept</span>
</div>
<script>
	(function ($) {
		const cookieBox = document.getElementById('js-cookie-box');
		const cookieButton = document.getElementById('js-cookie-button');
		if (!Cookies.get('cookie-box')) {
			cookieBox.classList.remove('cookie-box-hide');
			cookieButton.onclick = function () {
				Cookies.set('cookie-box', true, {
					expires: {{ site.Params.cookies.expire_days }}
				});
				cookieBox.classList.add('cookie-box-hide');
			};
		}
	})(jQuery);
</script>

<!-- cookie style -->
<style>
.cookie-box {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
  z-index: 9999;
  padding: 1rem 2rem;
  background: rgb(71, 71, 71);
  transition: all .75s cubic-bezier(.19, 1, .22, 1);
  color: #fdfdfd;
}

.cookie-box-hide {
  display: none;
}
</style>
{{ end }}
