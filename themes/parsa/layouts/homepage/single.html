{{ define "main" }}

{{ if eq .Params.layout "1" }}
<!-- featured post -->
<section class="overflow-hidden">
  <div class="container-fluid p-sm-0">
    <div class="row featured-post-slider">
      {{ range (where site.RegularPages "Type" "featured")}}
      <div class="col-lg-3 col-sm-6 mb-2 mb-lg-0 px-1">
        <article class="card bg-dark text-center text-white border-0 rounded-0">
          {{ partial "image.html" (dict "Src" .Params.Image "Alt" "post-thumb" "Class" "card-img rounded-0 img-fluid w-100" ) }}
          <div class="card-img-overlay">
            <div class="card-content">
              <p class="text-uppercase">{{range .Params.Categories}}{{. | humanize}}{{end}}</p>
              <h4 class="card-title mb-4"><a class="text-white" href="{{.Permalink}}">{{.Title}}</a></h4>
              <a class="btn btn-outline-light" href="{{.Permalink}}">read more</a>
            </div>
          </div>
        </article>
      </div>
      {{ end }}
    </div>
  </div>
</section>
<!-- /featured post -->

{{ else if (eq .Params.layout "2")}}

<!-- hero area -->
<section class="hero-section">
  <div class="container">
    <div class="row">
      <div class="col-lg-6 align-self-end">
        <h1 class="mb-0">Welcome</h1>
        <h2 class="mb-100 title-border-lg">to <i>{{site.Params.author}} Blog</i></h2>
        <p class="mb-80 mr-5">{{site.Params.bio | markdownify }}</p>
        <span class="font-secondary text-dark mr-3 mr-sm-5">Follow me :</span>
        <ul class="list-inline d-inline-block mb-5">
          {{ range site.Params.social }}
          <li class="list-inline-item mx-3"><a href="{{.URL}}" class="text-dark"><i class="{{.icon}}"></i></a></li>
          {{ end }}
        </ul>
      </div>
      <div class="col-lg-6 text-right">
        {{ partial "image.html" (dict "Src" site.Params.author_image "Alt" "banner-image" "Class" "img-fluid" ) }}
      </div>
    </div>
  </div>
</section>
<!-- /hero area -->

{{ else }}
<section class="section">
  <div class="container text-center">
    <h1>Please Enter A Valid Homepage Layout Number <br> (1 or 2)</h1>
  </div>
</section>
{{ end }}

<!-- blog post -->
<section class="section">
  <div class="container">
    {{ if eq .Params.layout "1" }}
    <div class="row masonry-container">
      {{ range site.RegularPages }}
      <div class="col-lg-4 col-sm-6 mb-5">
        <article class="text-center">
        {{ partial "image.html" (dict "Src" .Params.Image "Alt" "post-thumb" "Class" "img-fluid mb-4" ) }}
          <p class="text-uppercase mb-2">{{range .Params.Categories}}{{. | humanize}}{{end}}</p>
          <h4 class="title-border"><a class="text-dark" href="{{.Permalink}}">{{.Title}}</a></h4>
          <p>{{.Summary}}</p>
          <a href="{{.Permalink}}" class="btn btn-transparent">read more</a>
        </article>
      </div>
      {{ end }}
    </div>

    {{ else if (eq .Params.layout "2")}}

    <div class="row">
      {{ range $i,$p := (where site.RegularPages "Type" "post") }}
      <div class="col-12 mb-100">
        <article data-file="{{.Permalink}}" data-target="article"
          class="article-full-width {{if not (modBool $i 2)}} article-right {{ end }}">
          <div class="post-image">
            {{ partial "image.html" (dict "Src" .Params.Image "Alt" "post-thumb" "Class" "img-fluid" ) }}
          </div>
          <div class="post-content">
            <ul class="list-inline d-flex justify-content-between border-bottom post-meta pb-2 mb-4">
              <li class="list-inline-item"><i class="ti-calendar mr-2"></i>{{ .PublishDate.Format "Jan 02, 2006" }}</li>
              <li class="list-inline-item"><i class="ti-alarm-clock mr-2"></i><span class="eta"></span> read</li>
            </ul>
            <h4 class="mb-4"><a href="{{.Permalink}}" class="text-dark">{{ .Title }}</a></h4>
            <p class="mb-0 post-summary">{{.Summary }}</p>
            <a class="btn btn-transparent mb-4" href="{{.Permalink}}">Continue...</a>
          </div>
        </article>
      </div>
      {{ end }}
    </div>
    {{ end }}
  </div>
</section>
<!-- /blog post -->

{{partial "instafeed.html" . }}

{{ end }}