# 聯絡表單解決方案

## 🎯 推薦解決方案

### 方案一：Netlify Forms (推薦) ⭐⭐⭐⭐⭐

**優勢**：
- ✅ 完全免費（每月100次提交）
- ✅ 零配置，自動處理
- ✅ 內建垃圾郵件過濾
- ✅ 可設定通知郵件
- ✅ 與現有 Netlify 部署完美整合

**實施步驟**：
1. 在表單中添加 `netlify` 屬性
2. 設定通知郵件地址
3. 部署到 Netlify

### 方案二：Formspree (備用方案) ⭐⭐⭐⭐

**優勢**：
- ✅ 免費方案（每月50次提交）
- ✅ 簡單易用
- ✅ 支援多種通知方式
- ✅ 可自定義回覆郵件

**實施步驟**：
1. 註冊 Formspree 帳號
2. 獲取表單端點 URL
3. 更新 `contact_form_action` 配置

### 方案三：EmailJS (客戶端方案) ⭐⭐⭐

**優勢**：
- ✅ 純前端解決方案
- ✅ 免費方案（每月200封郵件）
- ✅ 支援多種郵件服務

**缺點**：
- ❌ 需要 JavaScript
- ❌ API 金鑰暴露在前端

## 🚀 立即實施 - Netlify Forms

### 步驟1：更新表單模板
```html
<form name="contact" netlify netlify-honeypot="bot-field" class="row mt-5" method="POST">
  <!-- 隱藏的蜜罐欄位防止垃圾郵件 -->
  <input type="hidden" name="bot-field" />
  
  <div class="col-lg-6">
    <input type="text" class="form-control mb-4" name="name" id="name" placeholder="Name" required>
  </div>
  <div class="col-lg-6">
    <input type="email" class="form-control mb-4" name="email" id="email" placeholder="Email" required>
  </div>
  <div class="col-12">
    <textarea name="message" id="message" class="form-control mb-4" placeholder="Message..." required></textarea>
  </div>
  <div class="col-12">
    <button type="submit" class="btn btn-primary">Submit</button>
  </div>
</form>
```

### 步驟2：設定成功頁面
創建 `/success/` 頁面用於表單提交成功後的重定向。

### 步驟3：配置通知郵件
在 Netlify 後台設定接收表單提交的郵件地址。

## 📧 其他方案詳細配置

### Formspree 配置
```toml
# hugo.toml
[params]
contact_form_action = "https://formspree.io/f/YOUR_FORM_ID"
```

### EmailJS 配置
需要添加 JavaScript 代碼來處理表單提交：
```javascript
// 使用 EmailJS API 發送郵件
emailjs.sendForm('service_id', 'template_id', form, 'user_id')
```

## 🔧 測試方法

### 本地測試
1. 使用 Netlify CLI 本地開發：
   ```bash
   netlify dev
   ```
2. 表單會在本地環境中模擬 Netlify Forms

### 生產環境測試
1. 部署到 Netlify
2. 提交測試表單
3. 檢查 Netlify 後台的 Forms 部分
4. 確認通知郵件正常接收

## 💰 成本比較

| 方案 | 免費額度 | 付費方案 | 推薦指數 |
|------|----------|----------|----------|
| Netlify Forms | 100次/月 | $19/月 | ⭐⭐⭐⭐⭐ |
| Formspree | 50次/月 | $10/月 | ⭐⭐⭐⭐ |
| EmailJS | 200封/月 | $15/月 | ⭐⭐⭐ |

## 🎯 建議

基於您的專案使用 Netlify 部署，**強烈推薦使用 Netlify Forms**：

1. **零額外成本**
2. **無需第三方服務**
3. **完美整合現有架構**
4. **企業級可靠性**

下一步：立即實施 Netlify Forms 解決方案！