baseURL = "https://example.com/"
title = "Innovate Solutions - Business Excellence & Strategic Insights"
pagination.paginate = 6
theme = "parsa"

enableRobotsTXT = true
buildDrafts = false
buildFuture = false
buildExpired = false
googleAnalytics = ""
languageCode = "en"
hasCJKLanguage = false
defaultContentLanguage = "en"

[minify]
disableXML = true
minifyOutput = true

[markup]
  [markup.highlight]
  noClasses = false
  codeFences = true
  guessSyntax = true
  lineNos = false
  style = "github"
  [markup.goldmark.renderer]
  unsafe = true
  [markup.tableOfContents]
  endLevel = 3
  ordered = false
  startLevel = 2

  # Mermaid configuration
  [markup.goldmark.extensions.mermaid]
  enable = true

  # Ensure recognition of mermaid code blocks
  [markup.goldmark.parser]
  autoHeadingID = true
  autoHeadingIDType = "github"
  [markup.goldmark.parser.attribute]
  block = true
  title = true

# URL sanitization settings
[permalinks]
  tags = "/tags/:slug/"
  categories = "/categories/:slug/"

[taxonomies]
  tag = "tags"
  category = "categories"
  series = "series"

# Custom tag aliases to handle problematic characters
[params.taxonomyAliases]
  "c#" = "csharp"

[params]
# Homepage layout (1 or 2)
layout = "2"

# URL sanitization for tags and categories
removePathAccents = true

# Website interface settings
logo = "images/logo.svg"
home = "Home"

# Environment settings
env = "production"

# Facebook settings
[params.facebook]
pixel_id = ""

# Google AdSense settings
[params.adsense]
enabled = false
client = ""
inArticleSlot = ""
description = "In-depth sharing of the latest technology trends, programming skills, and software development knowledge. A technical blog created for technology enthusiasts and professional developers, providing practical programming tutorials, technical guides, and industry insights to help you master the core skills of software development. Elder Yin's Technical Notes - An in-depth and accessible platform sharing the latest technology trends, programming techniques, and software development knowledge. A technical blog designed for tech enthusiasts and professional developers, offering practical programming tutorials, technical guides, and industry insights to help you master core skills in software development."
keywords = [
  "Programming",
  "Software Development",
  "Technology Trends",
  "Tech Tutorials",
  "Developer Guides",
  "Coding Techniques",
  "IT Industry Insights",
  "Tech Blog",
  "Programmer Resources",
  "Technological Innovation",
  "Business Technology Strategy",
  "Technical Leadership and Innovation",
  "Enterprise System Architecture Design"
]
author = "Demo User"
images = ["/images/demo-image.png"]
DateFormat = "2006-01-02"
defaultTheme = "auto"
disableThemeToggle = false
isCJKLanguage = true
ShowReadingTime = true
ShowShareButtons = true
ShowPostNavLinks = true
ShowBreadCrumbs = true
ShowCodeCopyButtons = true
ShowWordCount = true
ShowRssButtonInSectionTermList = true
UseHugoToc = true
disableSpecial1stPost = false
disableScrollToTop = false
comments = true
hidemeta = false
hideSummary = false
showtoc = true
tocopen = false
math = true

[params.assets]
favicon = "<link / abs url>"
favicon16x16 = "<link / abs url>"
favicon32x32 = "<link / abs url>"
apple_touch_icon = "<link / abs url>"
safari_pinned_tab = "<link / abs url>"

[params.label]
text = "Innovate Solutions - Business Excellence & Strategic Insights"
icon = "/favicon.png"
iconHeight = 35

[params.profileMode]
enabled = true
title = "Innovate Solutions - Business Excellence & Strategic Insights"
subtitle = "Expert business consulting and strategic guidance for companies looking to transform, innovate, and grow in today's competitive market. Our team of experienced business professionals provides insights on digital transformation, marketing strategy, customer experience, and business innovation to help organizations achieve their goals and maximize their potential in the global marketplace."
imageUrl = "/images/InnovateSolutions.png"
imageWidth = 240
imageHeight = 240
imageTitle = "Innovate Solutions"

[[params.profileMode.buttons]]
name = "Blog"
url = "posts"

[[params.profileMode.buttons]]
name = "Article Tags"
url = "tags"

[[params.profileMode.buttons]]
name = "Business Cooperation"
url = "resume"

[params.homeInfoParams]
Title = "Hi there 👋"
Content = "Welcome to my blog"

[[params.social]]
name = "github"
url = "https://github.com/example"

[[params.social]]
name = "email"
url = "mailto:<EMAIL>"

[[params.social]]
name = "linkedin"
url = "https://www.linkedin.com/company/example"

[params.analytics.google]
SiteVerificationTag = ""
GoogleAnalyticsID = ""

[params.analytics.bing]
SiteVerificationTag = "XYZabc"

[params.analytics.yandex]
SiteVerificationTag = "XYZabc"

[params.cover]
hidden = true
hiddenInList = true
hiddenInSingle = true

[params.editPost]
URL = "https://github.com/<path_to_repo>/content"
Text = "Suggest Changes"
appendFilePath = true

[params.fuseOpts]
isCaseSensitive = false
shouldSort = true
location = 0
distance = 1000
threshold = 0.4
minMatchCharLength = 0
keys = ["title", "permalink", "summary", "content"]

[params.Adsclient]
value = ""

[params.AdsinArticleSlot]
value = ""

[params.gcs_engine_id]
value = ""

[utteranc]
enable = false
repo = "example/comments"
issueTerm = "pathname"
theme = "github-light"

# CSS Plugins
[[params.plugins.css]]
link = "plugins/bootstrap/bootstrap.min.css"
[[params.plugins.css]]
link = "plugins/slick/slick.css"
[[params.plugins.css]]
link = "plugins/themify-icons/themify-icons.css"

# JS Plugins
[[params.plugins.js]]
link = "plugins/jQuery/jquery.min.js"
[[params.plugins.js]]
link = "plugins/bootstrap/bootstrap.min.js"
[[params.plugins.js]]
link = "plugins/slick/slick.min.js"
[[params.plugins.js]]
link = "plugins/headroom/headroom.js"
[[params.plugins.js]]
link = "plugins/instafeed/instafeed.min.js"
[[params.plugins.js]]
link = "plugins/masonry/masonry.js"
[[params.plugins.js]]
link = "plugins/reading-time/readingTime.min.js"
[[params.plugins.js]]
link = "plugins/smooth-scroll/smooth-scroll.js"
[[params.plugins.js]]
link = "plugins/search/fuse.min.js"
[[params.plugins.js]]
link = "plugins/search/mark.js"
[[params.plugins.js]]
link = "plugins/search/search.js"

[outputs]
home = ["HTML", "RSS", "JSON"]

[sitemap]
changeFreq = ""
disable = false
filename = "sitemap.xml"
priority = -1

# Menu items
# Main menu items
# Note: Temporarily hide the About menu item until content is created
 [[menu.main]]
 identifier = "about"
 name = "About Us"
 url = "/about/"
 weight = 5

[[menu.main]]
identifier = "blog"
name = "📚 Blog"
url = "/blog/"
weight = 10

[[menu.main]]
identifier = "archives"
name = "Article List"
url = "/archives/"
weight = 30

[[menu.main]]
identifier = "tags"
name = "Tags"
url = "/tags/"
weight = 40

[[menu.main]]
identifier = "search"
name = "Article Search"
url = "/search/"
weight = 50

[[menu.main]]
identifier = "contact"
name = "Contact Us"
url = "/contact/"
weight = 60
